[{"E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "5", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "6", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "7", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "9", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "10", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "11", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "12", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "13", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "14", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "15", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "16", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "17", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "18", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "19", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "20", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "21", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "22", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "32", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "33", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "37", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "38", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "39", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "40", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "41", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "42", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "43", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "44", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "45", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "46", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "47", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "48", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "49", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "50", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "51", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "52", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "53", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "54", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "55", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "56", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "57", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "58", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "59", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "60", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "61", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "62", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "63", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "64", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "65", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "66", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "68", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "69", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "70", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "71", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "72", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "73", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "74", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "75", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "78", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "79", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "80", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "81", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "82", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "83", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "84", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "85", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "86", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "87", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "88", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "90", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "93", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "94", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx": "95", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx": "96"}, {"size": 604, "mtime": 1753074278572, "results": "97", "hashOfConfig": "98"}, {"size": 440, "mtime": 1753074278578, "results": "99", "hashOfConfig": "98"}, {"size": 2367, "mtime": 1754027338559, "results": "100", "hashOfConfig": "98"}, {"size": 3344, "mtime": 1754027342330, "results": "101", "hashOfConfig": "98"}, {"size": 6890, "mtime": 1754027338913, "results": "102", "hashOfConfig": "98"}, {"size": 3102, "mtime": 1754027341616, "results": "103", "hashOfConfig": "98"}, {"size": 246993, "mtime": 1754368089934, "results": "104", "hashOfConfig": "98"}, {"size": 3112, "mtime": 1754027341569, "results": "105", "hashOfConfig": "98"}, {"size": 193, "mtime": 1753074278498, "results": "106", "hashOfConfig": "98"}, {"size": 394354, "mtime": 1754027342341, "results": "107", "hashOfConfig": "98"}, {"size": 3927, "mtime": 1753074357719, "results": "108", "hashOfConfig": "98"}, {"size": 6144, "mtime": 1753074357719, "results": "109", "hashOfConfig": "98"}, {"size": 1898, "mtime": 1753074357704, "results": "110", "hashOfConfig": "98"}, {"size": 56751, "mtime": 1754027342141, "results": "111", "hashOfConfig": "98"}, {"size": 1956, "mtime": 1754027412768, "results": "112", "hashOfConfig": "98"}, {"size": 9087, "mtime": 1754027342096, "results": "113", "hashOfConfig": "98"}, {"size": 41504, "mtime": 1754027341241, "results": "114", "hashOfConfig": "98"}, {"size": 5065, "mtime": 1753074278539, "results": "115", "hashOfConfig": "98"}, {"size": 13621, "mtime": 1754027341625, "results": "116", "hashOfConfig": "98"}, {"size": 3731, "mtime": 1754027340032, "results": "117", "hashOfConfig": "98"}, {"size": 13422, "mtime": 1754027412764, "results": "118", "hashOfConfig": "98"}, {"size": 299701, "mtime": 1754378419455, "results": "119", "hashOfConfig": "98"}, {"size": 9097, "mtime": 1754027339036, "results": "120", "hashOfConfig": "98"}, {"size": 30700, "mtime": 1754027339074, "results": "121", "hashOfConfig": "98"}, {"size": 3000, "mtime": 1754027339497, "results": "122", "hashOfConfig": "98"}, {"size": 2606, "mtime": 1754027338590, "results": "123", "hashOfConfig": "98"}, {"size": 33261, "mtime": 1754378419476, "results": "124", "hashOfConfig": "98"}, {"size": 23356, "mtime": 1754027340164, "results": "125", "hashOfConfig": "98"}, {"size": 13556, "mtime": 1754027338600, "results": "126", "hashOfConfig": "98"}, {"size": 27068, "mtime": 1754027338605, "results": "127", "hashOfConfig": "98"}, {"size": 61349, "mtime": 1754378419458, "results": "128", "hashOfConfig": "98"}, {"size": 32522, "mtime": 1754301482647, "results": "129", "hashOfConfig": "98"}, {"size": 7599, "mtime": 1754027341833, "results": "130", "hashOfConfig": "98"}, {"size": 11310, "mtime": 1754027341721, "results": "131", "hashOfConfig": "98"}, {"size": 24200, "mtime": 1753074357688, "results": "132", "hashOfConfig": "98"}, {"size": 4880, "mtime": 1753788908098, "results": "133", "hashOfConfig": "98"}, {"size": 743, "mtime": 1753074278497, "results": "134", "hashOfConfig": "98"}, {"size": 9238, "mtime": 1753074357704, "results": "135", "hashOfConfig": "98"}, {"size": 2608, "mtime": 1753074357704, "results": "136", "hashOfConfig": "98"}, {"size": 1297, "mtime": 1753074357704, "results": "137", "hashOfConfig": "98"}, {"size": 1248, "mtime": 1753074278585, "results": "138", "hashOfConfig": "98"}, {"size": 2715, "mtime": 1754378419463, "results": "139", "hashOfConfig": "98"}, {"size": 3279, "mtime": 1753074278540, "results": "140", "hashOfConfig": "98"}, {"size": 2997, "mtime": 1754027341160, "results": "141", "hashOfConfig": "98"}, {"size": 2052, "mtime": 1754027338587, "results": "142", "hashOfConfig": "98"}, {"size": 20180, "mtime": 1754027412766, "results": "143", "hashOfConfig": "98"}, {"size": 14238, "mtime": 1753074357719, "results": "144", "hashOfConfig": "98"}, {"size": 24844, "mtime": 1754027341285, "results": "145", "hashOfConfig": "98"}, {"size": 7772, "mtime": 1754027341357, "results": "146", "hashOfConfig": "98"}, {"size": 1962, "mtime": 1753074278501, "results": "147", "hashOfConfig": "98"}, {"size": 29119, "mtime": 1754027339100, "results": "148", "hashOfConfig": "98"}, {"size": 27242, "mtime": 1754027339226, "results": "149", "hashOfConfig": "98"}, {"size": 702, "mtime": 1754027339290, "results": "150", "hashOfConfig": "98"}, {"size": 2423, "mtime": 1754027339222, "results": "151", "hashOfConfig": "98"}, {"size": 13889, "mtime": 1754027339171, "results": "152", "hashOfConfig": "98"}, {"size": 28620, "mtime": 1754027341313, "results": "153", "hashOfConfig": "98"}, {"size": 15942, "mtime": 1754027341255, "results": "154", "hashOfConfig": "98"}, {"size": 6245, "mtime": 1753074278542, "results": "155", "hashOfConfig": "98"}, {"size": 19208, "mtime": 1754378419473, "results": "156", "hashOfConfig": "98"}, {"size": 20321, "mtime": 1754027341210, "results": "157", "hashOfConfig": "98"}, {"size": 6625, "mtime": 1754027341098, "results": "158", "hashOfConfig": "98"}, {"size": 2848, "mtime": 1753074278531, "results": "159", "hashOfConfig": "98"}, {"size": 3236, "mtime": 1753074278529, "results": "160", "hashOfConfig": "98"}, {"size": 15258, "mtime": 1754378419460, "results": "161", "hashOfConfig": "98"}, {"size": 29744, "mtime": 1754027338596, "results": "162", "hashOfConfig": "98"}, {"size": 2034, "mtime": 1754027338593, "results": "163", "hashOfConfig": "98"}, {"size": 15261, "mtime": 1754027339244, "results": "164", "hashOfConfig": "98"}, {"size": 32699, "mtime": 1754027338631, "results": "165", "hashOfConfig": "98"}, {"size": 16126, "mtime": 1753090913111, "results": "166", "hashOfConfig": "98"}, {"size": 11208, "mtime": 1754027340876, "results": "167", "hashOfConfig": "98"}, {"size": 8476, "mtime": 1754027340733, "results": "168", "hashOfConfig": "98"}, {"size": 15571, "mtime": 1754027341592, "results": "169", "hashOfConfig": "98"}, {"size": 17396, "mtime": 1754027340543, "results": "170", "hashOfConfig": "98"}, {"size": 26698, "mtime": 1754049162378, "results": "171", "hashOfConfig": "98"}, {"size": 61442, "mtime": 1754027412752, "results": "172", "hashOfConfig": "98"}, {"size": 5258, "mtime": 1754027341476, "results": "173", "hashOfConfig": "98"}, {"size": 883, "mtime": 1753074278558, "results": "174", "hashOfConfig": "98"}, {"size": 5504, "mtime": 1754027339188, "results": "175", "hashOfConfig": "98"}, {"size": 33119, "mtime": 1754027339153, "results": "176", "hashOfConfig": "98"}, {"size": 2196, "mtime": 1754027339483, "results": "177", "hashOfConfig": "98"}, {"size": 1092, "mtime": 1754027342066, "results": "178", "hashOfConfig": "98"}, {"size": 37230, "mtime": 1754027339142, "results": "179", "hashOfConfig": "98"}, {"size": 7943, "mtime": 1753074357704, "results": "180", "hashOfConfig": "98"}, {"size": 27589, "mtime": 1754027338693, "results": "181", "hashOfConfig": "98"}, {"size": 17392, "mtime": 1754027412754, "results": "182", "hashOfConfig": "98"}, {"size": 10829, "mtime": 1754027338799, "results": "183", "hashOfConfig": "98"}, {"size": 2669, "mtime": 1753074278525, "results": "184", "hashOfConfig": "98"}, {"size": 2931, "mtime": 1753074357657, "results": "185", "hashOfConfig": "98"}, {"size": 15667, "mtime": 1754027338685, "results": "186", "hashOfConfig": "98"}, {"size": 677, "mtime": 1754027342255, "results": "187", "hashOfConfig": "98"}, {"size": 6886, "mtime": 1754027342000, "results": "188", "hashOfConfig": "98"}, {"size": 7158, "mtime": 1754027341850, "results": "189", "hashOfConfig": "98"}, {"size": 1211, "mtime": 1754027341947, "results": "190", "hashOfConfig": "98"}, {"size": 10160, "mtime": 1754027339317, "results": "191", "hashOfConfig": "98"}, {"size": 3836, "mtime": 1754027338584, "results": "192", "hashOfConfig": "98"}, {"size": 3021, "mtime": 1754027338583, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "n0kw7i", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 224, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["482", "483"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["484"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["485", "486", "487", "488", "489", "490"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["715", "716"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["738"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["739"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["740", "741", "742", "743"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["744", "745", "746"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["800"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["825", "826", "827", "828", "829", "830", "831", "832"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["860", "861", "862", "863"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1128", "1129", "1130", "1131", "1132", "1133"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1134", "1135", "1136", "1137"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1138", "1139"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1140", "1141", "1142"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1143", "1144", "1145"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1146"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1147", "1148"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1163", "1164", "1165"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1288", "1289", "1290"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1318", "1319", "1320", "1321"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1342", "1343", "1344", "1345", "1346", "1347"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1348", "1349"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1350", "1351", "1352"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1399", "1400", "1401", "1402", "1403"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1647", "1648"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1856", "1857"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1885"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1886", "1887", "1888"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1889", "1890"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1891"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx", ["1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx", [], [], {"ruleId": "1912", "severity": 1, "message": "1913", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1916", "line": 9, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "1917", "line": 1, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "1918", "line": 2, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "1919", "line": 7, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "1920", "line": 7, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "1921", "line": 98, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 98, "endColumn": 25}, {"ruleId": "1922", "severity": 1, "message": "1923", "line": 103, "column": 6, "nodeType": "1924", "endLine": 103, "endColumn": 8, "suggestions": "1925"}, {"ruleId": "1912", "severity": 1, "message": "1926", "line": 148, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 148, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 1, "column": 58, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 65}, {"ruleId": "1912", "severity": 1, "message": "1928", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "1929", "line": 6, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1930", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "1931", "line": 14, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 40}, {"ruleId": "1912", "severity": 1, "message": "1932", "line": 19, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "1933", "line": 24, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1934", "line": 25, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1935", "line": 26, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "1936", "line": 27, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1937", "line": 28, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 28, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1938", "line": 29, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "1939", "line": 30, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 30, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1940", "line": 31, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "1941", "line": 32, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "1942", "line": 33, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 33, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1943", "line": 34, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1944", "line": 35, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 35, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "1945", "line": 36, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 36, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "1946", "line": 37, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 37, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1947", "line": 39, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "1948", "line": 40, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1949", "line": 41, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 41, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1950", "line": 42, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 42, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 46, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1952", "line": 52, "column": 20, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 62, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 62, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "1954", "line": 63, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 63, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "1955", "line": 70, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 70, "endColumn": 6}, {"ruleId": "1912", "severity": 1, "message": "1956", "line": 71, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 71, "endColumn": 6}, {"ruleId": "1912", "severity": 1, "message": "1957", "line": 78, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 78, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "1958", "line": 80, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1959", "line": 81, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "1960", "line": 81, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "1961", "line": 84, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "1962", "line": 85, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1963", "line": 86, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 86, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1964", "line": 90, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 90, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "1965", "line": 92, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "1966", "line": 98, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 98, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1967", "line": 105, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "1968", "line": 108, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 108, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "1969", "line": 109, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 109, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "1970", "line": 114, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 114, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "1971", "line": 114, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 114, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "1972", "line": 117, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 117, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1973", "line": 124, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 124, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1974", "line": 137, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 137, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1975", "line": 200, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 200, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1976", "line": 217, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 217, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1977", "line": 225, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 225, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "1978", "line": 381, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 381, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1979", "line": 416, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 416, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "1980", "line": 418, "column": 6, "nodeType": "1914", "messageId": "1915", "endLine": 418, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "1981", "line": 421, "column": 6, "nodeType": "1914", "messageId": "1915", "endLine": 421, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "1982", "line": 437, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 437, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1983", "line": 438, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 438, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "1984", "line": 440, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 440, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "1985", "line": 443, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 443, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "1986", "line": 447, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 447, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "1987", "line": 448, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 448, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "1988", "line": 459, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 459, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "1989", "line": 460, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 460, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1990", "line": 461, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 461, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1991", "line": 463, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 463, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "1992", "line": 463, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 463, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "1993", "line": 468, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 468, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "1994", "line": 468, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 468, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "1995", "line": 470, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 470, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1996", "line": 470, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 470, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "1997", "line": 473, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 473, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1998", "line": 473, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 473, "endColumn": 40}, {"ruleId": "1912", "severity": 1, "message": "1999", "line": 474, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 474, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2000", "line": 479, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 479, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2001", "line": 479, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 479, "endColumn": 50}, {"ruleId": "1912", "severity": 1, "message": "2002", "line": 486, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 486, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2003", "line": 486, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 486, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2004", "line": 488, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 488, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2005", "line": 488, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 488, "endColumn": 41}, {"ruleId": "1912", "severity": 1, "message": "2006", "line": 490, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 490, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2007", "line": 490, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 490, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2008", "line": 504, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 504, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2009", "line": 505, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 505, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2010", "line": 505, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 505, "endColumn": 58}, {"ruleId": "1912", "severity": 1, "message": "2011", "line": 508, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 508, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2012", "line": 508, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 508, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2013", "line": 509, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 509, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2014", "line": 509, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 509, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2015", "line": 510, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 510, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2016", "line": 510, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 510, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2017", "line": 519, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 519, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2018", "line": 520, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 520, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2019", "line": 526, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 526, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2020", "line": 530, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 530, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2021", "line": 530, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 530, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2022", "line": 533, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 533, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2023", "line": 533, "column": 20, "nodeType": "1914", "messageId": "1915", "endLine": 533, "endColumn": 32}, {"ruleId": "1922", "severity": 1, "message": "2024", "line": 573, "column": 5, "nodeType": "1924", "endLine": 573, "endColumn": 27, "suggestions": "2025"}, {"ruleId": "1912", "severity": 1, "message": "2026", "line": 583, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 583, "endColumn": 6}, {"ruleId": "1912", "severity": 1, "message": "2027", "line": 584, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 584, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 585, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 585, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2029", "line": 587, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 587, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2030", "line": 588, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 588, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2031", "line": 593, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 593, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2032", "line": 594, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 594, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2033", "line": 629, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 629, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2034", "line": 630, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 630, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2035", "line": 631, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 631, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2036", "line": 639, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 639, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 641, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 641, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2038", "line": 642, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 642, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2039", "line": 643, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 643, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2040", "line": 644, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 644, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2041", "line": 649, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 649, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2042", "line": 651, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 651, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2043", "line": 653, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 653, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2044", "line": 663, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 663, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2045", "line": 664, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 664, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2046", "line": 667, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 667, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2047", "line": 671, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 671, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2048", "line": 673, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 673, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2049", "line": 674, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 674, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2050", "line": 676, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 676, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2051", "line": 683, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 683, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2052", "line": 684, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 684, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2053", "line": 689, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 689, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2054", "line": 690, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 690, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2055", "line": 691, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 691, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2056", "line": 701, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 701, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2057", "line": 705, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 705, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2058", "line": 709, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 709, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2059", "line": 711, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 711, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2060", "line": 713, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 713, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2061", "line": 714, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 714, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2062", "line": 719, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 719, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2063", "line": 720, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 720, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2064", "line": 738, "column": 18, "nodeType": "1914", "messageId": "1915", "endLine": 738, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2065", "line": 739, "column": 18, "nodeType": "1914", "messageId": "1915", "endLine": 739, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2066", "line": 743, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 743, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2067", "line": 755, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 755, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2068", "line": 788, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 788, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2069", "line": 799, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 799, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2070", "line": 804, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 804, "endColumn": 42}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 809, "column": 22, "nodeType": "2073", "messageId": "2074", "endLine": 809, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2075", "line": 895, "column": 5, "nodeType": "1924", "endLine": 895, "endColumn": 46, "suggestions": "2076"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 895, "column": 6, "nodeType": "2078", "endLine": 895, "endColumn": 29}, {"ruleId": "1922", "severity": 1, "message": "2079", "line": 913, "column": 5, "nodeType": "1924", "endLine": 913, "endColumn": 18, "suggestions": "2080"}, {"ruleId": "1912", "severity": 1, "message": "2081", "line": 915, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 915, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2082", "line": 916, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 916, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2083", "line": 937, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 937, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2084", "line": 998, "column": 5, "nodeType": "1924", "endLine": 1006, "endColumn": 3, "suggestions": "2085"}, {"ruleId": "1922", "severity": 1, "message": "2086", "line": 1034, "column": 5, "nodeType": "1924", "endLine": 1057, "endColumn": 3, "suggestions": "2087"}, {"ruleId": "1922", "severity": 1, "message": "2088", "line": 1069, "column": 5, "nodeType": "1924", "endLine": 1069, "endColumn": 19, "suggestions": "2089"}, {"ruleId": "1922", "severity": 1, "message": "2090", "line": 1180, "column": 5, "nodeType": "1924", "endLine": 1180, "endColumn": 39, "suggestions": "2091"}, {"ruleId": "1912", "severity": 1, "message": "2092", "line": 1299, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 1299, "endColumn": 24}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1390, "column": 25, "nodeType": "2073", "messageId": "2074", "endLine": 1390, "endColumn": 27}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1397, "column": 25, "nodeType": "2073", "messageId": "2074", "endLine": 1397, "endColumn": 27}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1397, "column": 53, "nodeType": "2073", "messageId": "2074", "endLine": 1397, "endColumn": 55}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1400, "column": 26, "nodeType": "2073", "messageId": "2074", "endLine": 1400, "endColumn": 28}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1400, "column": 58, "nodeType": "2073", "messageId": "2074", "endLine": 1400, "endColumn": 60}, {"ruleId": "1912", "severity": 1, "message": "2094", "line": 1531, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1531, "endColumn": 33}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 1608, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 1608, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2095", "line": 1755, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1755, "endColumn": 30}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 2017, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 2017, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 2189, "column": 25, "nodeType": "2073", "messageId": "2074", "endLine": 2189, "endColumn": 27}, {"ruleId": "1922", "severity": 1, "message": "2096", "line": 2228, "column": 5, "nodeType": "1924", "endLine": 2228, "endColumn": 69, "suggestions": "2097"}, {"ruleId": "1912", "severity": 1, "message": "2098", "line": 2285, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 2285, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2099", "line": 2292, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 2292, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2100", "line": 2295, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 2295, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2101", "line": 2295, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 2295, "endColumn": 48}, {"ruleId": "1912", "severity": 1, "message": "2102", "line": 2687, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 2687, "endColumn": 27}, {"ruleId": "1922", "severity": 1, "message": "2103", "line": 2722, "column": 5, "nodeType": "1924", "endLine": 2722, "endColumn": 38, "suggestions": "2104"}, {"ruleId": "1912", "severity": 1, "message": "2105", "line": 2739, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 2739, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2106", "line": 2773, "column": 6, "nodeType": "1914", "messageId": "1915", "endLine": 2773, "endColumn": 18}, {"ruleId": "1922", "severity": 1, "message": "2107", "line": 3157, "column": 4, "nodeType": "1924", "endLine": 3157, "endColumn": 18, "suggestions": "2108"}, {"ruleId": "1912", "severity": 1, "message": "2109", "line": 3504, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3504, "endColumn": 33}, {"ruleId": "1922", "severity": 1, "message": "2110", "line": 3578, "column": 16, "nodeType": "2078", "endLine": 3578, "endColumn": 37}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3579, "column": 56, "nodeType": "2073", "messageId": "2074", "endLine": 3579, "endColumn": 58}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3583, "column": 49, "nodeType": "2073", "messageId": "2074", "endLine": 3583, "endColumn": 51}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3587, "column": 50, "nodeType": "2073", "messageId": "2074", "endLine": 3587, "endColumn": 52}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3593, "column": 51, "nodeType": "2073", "messageId": "2074", "endLine": 3593, "endColumn": 53}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3600, "column": 51, "nodeType": "2073", "messageId": "2074", "endLine": 3600, "endColumn": 53}, {"ruleId": "1912", "severity": 1, "message": "2111", "line": 3826, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3826, "endColumn": 23}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 3834, "column": 30, "nodeType": "2073", "messageId": "2074", "endLine": 3834, "endColumn": 32}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3847, "column": 39, "nodeType": "2073", "messageId": "2074", "endLine": 3847, "endColumn": 41}, {"ruleId": "1922", "severity": 1, "message": "2112", "line": 3861, "column": 5, "nodeType": "1924", "endLine": 3861, "endColumn": 33, "suggestions": "2113"}, {"ruleId": "1912", "severity": 1, "message": "2114", "line": 3865, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 3865, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2115", "line": 3865, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 3865, "endColumn": 52}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 3962, "column": 55, "nodeType": "2073", "messageId": "2074", "endLine": 3962, "endColumn": 57}, {"ruleId": "1912", "severity": 1, "message": "2116", "line": 3981, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3981, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2117", "line": 3983, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 3983, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2118", "line": 3987, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3987, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2119", "line": 4006, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 4006, "endColumn": 26}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 4030, "column": 66, "nodeType": "2073", "messageId": "2074", "endLine": 4030, "endColumn": 68}, {"ruleId": "1922", "severity": 1, "message": "2120", "line": 4037, "column": 5, "nodeType": "1924", "endLine": 4044, "endColumn": 3, "suggestions": "2121"}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 4278, "column": 17, "nodeType": "2073", "messageId": "2074", "endLine": 4278, "endColumn": 19}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 4532, "column": 21, "nodeType": "2073", "messageId": "2074", "endLine": 4532, "endColumn": 23}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 4540, "column": 21, "nodeType": "2073", "messageId": "2074", "endLine": 4540, "endColumn": 23}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 4553, "column": 15, "nodeType": "2073", "messageId": "2074", "endLine": 4553, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2122", "line": 4850, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 4850, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2123", "line": 4861, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 4861, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2124", "line": 4862, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 4862, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2125", "line": 4868, "column": 5, "nodeType": "1924", "endLine": 4868, "endColumn": 62, "suggestions": "2126"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 4868, "column": 6, "nodeType": "2127", "endLine": 4868, "endColumn": 48}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 4891, "column": 25, "nodeType": "2073", "messageId": "2074", "endLine": 4891, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2128", "line": 4895, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4895, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2129", "line": 4918, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 4918, "endColumn": 23}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 4993, "column": 25, "nodeType": "2073", "messageId": "2074", "endLine": 4993, "endColumn": 27}, {"ruleId": "1922", "severity": 1, "message": "2130", "line": 5026, "column": 5, "nodeType": "1924", "endLine": 5026, "endColumn": 22, "suggestions": "2131"}, {"ruleId": "1912", "severity": 1, "message": "2132", "line": 5028, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 5028, "endColumn": 18}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 5030, "column": 40, "nodeType": "2073", "messageId": "2074", "endLine": 5030, "endColumn": 42}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 5095, "column": 69, "nodeType": "2073", "messageId": "2074", "endLine": 5095, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2133", "line": 5145, "column": 12, "nodeType": "1914", "messageId": "1915", "endLine": 5145, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2134", "line": 5146, "column": 12, "nodeType": "1914", "messageId": "1915", "endLine": 5146, "endColumn": 22}, {"ruleId": "1922", "severity": 1, "message": "2135", "line": 5176, "column": 5, "nodeType": "1924", "endLine": 5176, "endColumn": 38, "suggestions": "2136"}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 5179, "column": 40, "nodeType": "2073", "messageId": "2074", "endLine": 5179, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2133", "line": 5185, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5185, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2134", "line": 5186, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5186, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2137", "line": 5192, "column": 5, "nodeType": "1924", "endLine": 5192, "endColumn": 106, "suggestions": "2138"}, {"ruleId": "1922", "severity": 1, "message": "2139", "line": 5341, "column": 5, "nodeType": "1924", "endLine": 5341, "endColumn": 17, "suggestions": "2140"}, {"ruleId": "1922", "severity": 1, "message": "2141", "line": 5357, "column": 5, "nodeType": "1924", "endLine": 5357, "endColumn": 78, "suggestions": "2142"}, {"ruleId": "1912", "severity": 1, "message": "2143", "line": 5360, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 5360, "endColumn": 29}, {"ruleId": "2144", "severity": 1, "message": "2145", "line": 6024, "column": 80, "nodeType": "2146", "messageId": "2147", "endLine": 6024, "endColumn": 81, "suggestions": "2148"}, {"ruleId": "1912", "severity": 1, "message": "2149", "line": 6239, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 6239, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2150", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2151", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 3, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2153", "line": 8, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2154", "line": 9, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2155", "line": 13, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 46}, {"ruleId": "2156", "severity": 1, "message": "2157", "line": 2400, "column": 5, "nodeType": "2158", "messageId": "2074", "endLine": 2400, "endColumn": 17}, {"ruleId": "2156", "severity": 1, "message": "2159", "line": 2401, "column": 5, "nodeType": "2158", "messageId": "2074", "endLine": 2401, "endColumn": 20}, {"ruleId": "2156", "severity": 1, "message": "2160", "line": 2732, "column": 5, "nodeType": "2158", "messageId": "2074", "endLine": 2732, "endColumn": 24}, {"ruleId": "2161", "severity": 1, "message": "2162", "line": 2738, "column": 31, "nodeType": "2078", "messageId": "2163", "endLine": 2738, "endColumn": 51}, {"ruleId": "2156", "severity": 1, "message": "2164", "line": 2909, "column": 5, "nodeType": "2158", "messageId": "2074", "endLine": 2909, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2165", "line": 3613, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 3613, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2165", "line": 3812, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 3812, "endColumn": 28}, {"ruleId": "2156", "severity": 1, "message": "2166", "line": 5247, "column": 5, "nodeType": "2158", "messageId": "2074", "endLine": 5247, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2167", "line": 5310, "column": 14, "nodeType": "1914", "messageId": "1915", "endLine": 5310, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2168", "line": 6401, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 6401, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2168", "line": 6427, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 6427, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2168", "line": 6433, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 6433, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2168", "line": 6448, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 6448, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2167", "line": 7225, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 7225, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2167", "line": 7469, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 7469, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2167", "line": 7640, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 7640, "endColumn": 16}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 8305, "column": 66, "nodeType": "2073", "messageId": "2074", "endLine": 8305, "endColumn": 68}, {"ruleId": "1912", "severity": 1, "message": "2169", "line": 70, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 70, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2170", "line": 1, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2171", "line": 226, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 226, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2172", "line": 296, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 296, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2173", "line": 681, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 681, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2174", "line": 686, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 686, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2175", "line": 1, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2176", "line": 3, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2177", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2178", "line": 2, "column": 44, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 56}, {"ruleId": "1912", "severity": 1, "message": "2179", "line": 18, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2180", "line": 19, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 20, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2182", "line": 21, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2183", "line": 24, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2184", "line": 25, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2185", "line": 26, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2186", "line": 31, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2187", "line": 38, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 38, "endColumn": 55}, {"ruleId": "1912", "severity": 1, "message": "2188", "line": 38, "column": 63, "nodeType": "1914", "messageId": "1915", "endLine": 38, "endColumn": 70}, {"ruleId": "1912", "severity": 1, "message": "2189", "line": 46, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2190", "line": 48, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2191", "line": 90, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 90, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2192", "line": 91, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 91, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2193", "line": 97, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 97, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2194", "line": 98, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 98, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2195", "line": 102, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 102, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2196", "line": 106, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2197", "line": 110, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 110, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2198", "line": 111, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 111, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2199", "line": 112, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 112, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2200", "line": 113, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 113, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2201", "line": 114, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 114, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 117, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 117, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2203", "line": 118, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 118, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2204", "line": 160, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 160, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2205", "line": 170, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 170, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2206", "line": 178, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 178, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2207", "line": 179, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 179, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2208", "line": 180, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 180, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2209", "line": 181, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 181, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2210", "line": 182, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 182, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2211", "line": 203, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 203, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2212", "line": 207, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 207, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2213", "line": 453, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 453, "endColumn": 26}, {"ruleId": "1922", "severity": 1, "message": "2214", "line": 545, "column": 5, "nodeType": "1924", "endLine": 545, "endColumn": 60, "suggestions": "2215"}, {"ruleId": "1912", "severity": 1, "message": "2216", "line": 559, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 559, "endColumn": 22}, {"ruleId": "1922", "severity": 1, "message": "2217", "line": 579, "column": 5, "nodeType": "1924", "endLine": 579, "endColumn": 60, "suggestions": "2218"}, {"ruleId": "1922", "severity": 1, "message": "2219", "line": 596, "column": 4, "nodeType": "1924", "endLine": 596, "endColumn": 6, "suggestions": "2220"}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "1961", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "1962", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "1959", "line": 5, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "1960", "line": 5, "column": 46, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 55}, {"ruleId": "1912", "severity": 1, "message": "2222", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "1971", "line": 11, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2223", "line": 18, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2224", "line": 21, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2225", "line": 22, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2226", "line": 23, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2004", "line": 32, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2005", "line": 32, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "1936", "line": 6, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2228", "line": 9, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2229", "line": 12, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2230", "line": 25, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2231", "line": 53, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2232", "line": 54, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2041", "line": 55, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 55, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2233", "line": 56, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 56, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2234", "line": 57, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 57, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2058", "line": 58, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2235", "line": 59, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2236", "line": 60, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2059", "line": 61, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2237", "line": 62, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 62, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2238", "line": 63, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 63, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2239", "line": 64, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 64, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2240", "line": 65, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 65, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2241", "line": 66, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 66, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2198", "line": 67, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 67, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2201", "line": 68, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 68, "endColumn": 23}, {"ruleId": "1922", "severity": 1, "message": "2242", "line": 85, "column": 5, "nodeType": "1924", "endLine": 85, "endColumn": 7, "suggestions": "2243"}, {"ruleId": "1922", "severity": 1, "message": "2244", "line": 103, "column": 5, "nodeType": "1924", "endLine": 103, "endColumn": 28, "suggestions": "2245"}, {"ruleId": "1922", "severity": 1, "message": "2246", "line": 114, "column": 5, "nodeType": "1924", "endLine": 114, "endColumn": 48, "suggestions": "2247"}, {"ruleId": "1912", "severity": 1, "message": "2248", "line": 193, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 193, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 2, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2249", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2250", "line": 8, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2251", "line": 8, "column": 44, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 59}, {"ruleId": "1912", "severity": 1, "message": "2252", "line": 10, "column": 34, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 57}, {"ruleId": "1912", "severity": 1, "message": "2253", "line": 10, "column": 59, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 79}, {"ruleId": "1912", "severity": 1, "message": "2254", "line": 59, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2255", "line": 124, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 124, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 1, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2256", "line": 8, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2250", "line": 9, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2257", "line": 80, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 58}, {"ruleId": "1922", "severity": 1, "message": "2258", "line": 86, "column": 8, "nodeType": "2259", "endLine": 90, "endColumn": 12}, {"ruleId": "1922", "severity": 1, "message": "2260", "line": 86, "column": 8, "nodeType": "2259", "endLine": 90, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2261", "line": 92, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2262", "line": 92, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 42}, {"ruleId": "2263", "severity": 1, "message": "2264", "line": 113, "column": 113, "nodeType": "2265", "messageId": "2266", "endLine": 113, "endColumn": 397}, {"ruleId": "1922", "severity": 1, "message": "2267", "line": 154, "column": 5, "nodeType": "1924", "endLine": 154, "endColumn": 38, "suggestions": "2268"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 154, "column": 6, "nodeType": "2073", "endLine": 154, "endColumn": 37}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 154, "column": 33, "nodeType": "2073", "messageId": "2074", "endLine": 154, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2254", "line": 156, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 156, "endColumn": 24}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 174, "column": 56, "nodeType": "2073", "messageId": "2074", "endLine": 174, "endColumn": 58}, {"ruleId": "1912", "severity": 1, "message": "2269", "line": 181, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 181, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2270", "line": 182, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 182, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2271", "line": 305, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 305, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2254", "line": 771, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 771, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2272", "line": 806, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 806, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2273", "line": 806, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 806, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2274", "line": 807, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 807, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2275", "line": 807, "column": 22, "nodeType": "1914", "messageId": "1915", "endLine": 807, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2100", "line": 808, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 808, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2101", "line": 808, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 808, "endColumn": 48}, {"ruleId": "1912", "severity": 1, "message": "2276", "line": 809, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 809, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2277", "line": 809, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 809, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2018", "line": 810, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 810, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2278", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2279", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2280", "line": 31, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2281", "line": 32, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2282", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "1931", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2283", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 9, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 11, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 12, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2285", "line": 14, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2185", "line": 16, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 18, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 19, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 20, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 21, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2290", "line": 22, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 23, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2292", "line": 24, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2293", "line": 25, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "1956", "line": 3, "column": 65, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 69}, {"ruleId": "1912", "severity": 1, "message": "2294", "line": 6, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2295", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 16, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2296", "line": 18, "column": 63, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 73}, {"ruleId": "1912", "severity": 1, "message": "2297", "line": 20, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2273", "line": 84, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2298", "line": 85, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2299", "line": 85, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2300", "line": 86, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 86, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2301", "line": 86, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 86, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2302", "line": 90, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 90, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2303", "line": 90, "column": 45, "nodeType": "1914", "messageId": "1915", "endLine": 90, "endColumn": 62}, {"ruleId": "1912", "severity": 1, "message": "2304", "line": 93, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 93, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2305", "line": 94, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 94, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2011", "line": 95, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 95, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2012", "line": 96, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 96, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2013", "line": 97, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 97, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2014", "line": 98, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 98, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2015", "line": 99, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 99, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2016", "line": 100, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 100, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2306", "line": 101, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 102, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 102, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2308", "line": 103, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 103, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 104, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2309", "line": 105, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2310", "line": 107, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 107, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2311", "line": 108, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 108, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2312", "line": 115, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 115, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2313", "line": 116, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 116, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2314", "line": 118, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 118, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2315", "line": 119, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 119, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2316", "line": 120, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 120, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2317", "line": 121, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 121, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2318", "line": 122, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 122, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2319", "line": 123, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 123, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2320", "line": 132, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 132, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2321", "line": 137, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 137, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2192", "line": 139, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 139, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2199", "line": 140, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 140, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2322", "line": 141, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 141, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2323", "line": 144, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 144, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2197", "line": 145, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 145, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2324", "line": 146, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 146, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2325", "line": 170, "column": 5, "nodeType": "1924", "endLine": 170, "endColumn": 45, "suggestions": "2326"}, {"ruleId": "1912", "severity": 1, "message": "2327", "line": 221, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 221, "endColumn": 29}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 241, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 241, "endColumn": 26}, {"ruleId": "1922", "severity": 1, "message": "2328", "line": 315, "column": 7, "nodeType": "1924", "endLine": 315, "endColumn": 42, "suggestions": "2329"}, {"ruleId": "1912", "severity": 1, "message": "2330", "line": 339, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 339, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2331", "line": 340, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 340, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2332", "line": 488, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 488, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2333", "line": 491, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 491, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2334", "line": 500, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 500, "endColumn": 31}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 811, "column": 26, "nodeType": "2073", "messageId": "2074", "endLine": 811, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2335", "line": 1031, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1031, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2336", "line": 1035, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1035, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2337", "line": 1039, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1039, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2338", "line": 1043, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1043, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2339", "line": 1047, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1047, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2340", "line": 1051, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1051, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2341", "line": 1055, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1055, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2342", "line": 1059, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1059, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2343", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2344", "line": 13, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2345", "line": 15, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2346", "line": 79, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 79, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2347", "line": 82, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2348", "line": 83, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 83, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2349", "line": 84, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2036", "line": 88, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 88, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2350", "line": 89, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 89, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2351", "line": 91, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 91, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2038", "line": 92, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2039", "line": 93, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 93, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 94, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 94, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 104, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 19}, {"ruleId": "1922", "severity": 1, "message": "2352", "line": 209, "column": 7, "nodeType": "1924", "endLine": 209, "endColumn": 9, "suggestions": "2353"}, {"ruleId": "1922", "severity": 1, "message": "2354", "line": 244, "column": 7, "nodeType": "1924", "endLine": 244, "endColumn": 29, "suggestions": "2355"}, {"ruleId": "1922", "severity": 1, "message": "2356", "line": 249, "column": 7, "nodeType": "1924", "endLine": 249, "endColumn": 18, "suggestions": "2357"}, {"ruleId": "1922", "severity": 1, "message": "2358", "line": 292, "column": 7, "nodeType": "1924", "endLine": 292, "endColumn": 72, "suggestions": "2359"}, {"ruleId": "1912", "severity": 1, "message": "2309", "line": 331, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 331, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2360", "line": 334, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 334, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2361", "line": 465, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 465, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2362", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2363", "line": 6, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2364", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2365", "line": 7, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2366", "line": 8, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2367", "line": 9, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2038", "line": 13, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2039", "line": 14, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 15, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2368", "line": 17, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2369", "line": 18, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2370", "line": 18, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2371", "line": 19, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2372", "line": 96, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 96, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2373", "line": 97, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 97, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2374", "line": 100, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 100, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2375", "line": 101, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2376", "line": 109, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 109, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2377", "line": 129, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 129, "endColumn": 16}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 176, "column": 45, "nodeType": "2073", "messageId": "2074", "endLine": 176, "endColumn": 47}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 176, "column": 104, "nodeType": "2073", "messageId": "2074", "endLine": 176, "endColumn": 106}, {"ruleId": "1912", "severity": 1, "message": "2186", "line": 2, "column": 60, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 73}, {"ruleId": "1912", "severity": 1, "message": "2362", "line": 3, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2378", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2379", "line": 9, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2380", "line": 133, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 133, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2381", "line": 134, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 134, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2382", "line": 135, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 135, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2383", "line": 135, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 135, "endColumn": 52}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 137, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 137, "endColumn": 19}, {"ruleId": "1922", "severity": 1, "message": "2384", "line": 163, "column": 8, "nodeType": "1924", "endLine": 163, "endColumn": 10, "suggestions": "2385"}, {"ruleId": "1912", "severity": 1, "message": "2386", "line": 299, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 299, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2387", "line": 342, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 342, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2388", "line": 343, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 343, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2389", "line": 344, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 344, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2390", "line": 346, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 346, "endColumn": 20}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 465, "column": 22, "nodeType": "2073", "messageId": "2074", "endLine": 465, "endColumn": 24}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 465, "column": 53, "nodeType": "2073", "messageId": "2074", "endLine": 465, "endColumn": 55}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 465, "column": 89, "nodeType": "2073", "messageId": "2074", "endLine": 465, "endColumn": 91}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 465, "column": 125, "nodeType": "2073", "messageId": "2074", "endLine": 465, "endColumn": 127}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 467, "column": 29, "nodeType": "2073", "messageId": "2074", "endLine": 467, "endColumn": 31}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 467, "column": 56, "nodeType": "2073", "messageId": "2074", "endLine": 467, "endColumn": 58}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 467, "column": 88, "nodeType": "2073", "messageId": "2074", "endLine": 467, "endColumn": 90}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 467, "column": 120, "nodeType": "2073", "messageId": "2074", "endLine": 467, "endColumn": 122}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 469, "column": 29, "nodeType": "2073", "messageId": "2074", "endLine": 469, "endColumn": 31}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 469, "column": 64, "nodeType": "2073", "messageId": "2074", "endLine": 469, "endColumn": 66}, {"ruleId": "1912", "severity": 1, "message": "2391", "line": 111, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 111, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2319", "line": 152, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 152, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2050", "line": 153, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 153, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2392", "line": 159, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 159, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2393", "line": 185, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 185, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2394", "line": 279, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 279, "endColumn": 29}, {"ruleId": "1922", "severity": 1, "message": "2395", "line": 460, "column": 5, "nodeType": "1924", "endLine": 460, "endColumn": 69, "suggestions": "2396"}, {"ruleId": "1922", "severity": 1, "message": "2395", "line": 497, "column": 5, "nodeType": "1924", "endLine": 497, "endColumn": 73, "suggestions": "2397"}, {"ruleId": "1922", "severity": 1, "message": "2395", "line": 712, "column": 5, "nodeType": "1924", "endLine": 712, "endColumn": 134, "suggestions": "2398"}, {"ruleId": "1922", "severity": 1, "message": "2395", "line": 742, "column": 5, "nodeType": "1924", "endLine": 742, "endColumn": 134, "suggestions": "2399"}, {"ruleId": "1922", "severity": 1, "message": "2395", "line": 776, "column": 5, "nodeType": "1924", "endLine": 776, "endColumn": 82, "suggestions": "2400"}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 819, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 819, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 820, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 820, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 821, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 821, "endColumn": 26}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 822, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 822, "endColumn": 26}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 826, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 826, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 827, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 827, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 828, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 828, "endColumn": 26}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 829, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 829, "endColumn": 26}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 833, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 833, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 834, "column": 24, "nodeType": "2073", "messageId": "2074", "endLine": 834, "endColumn": 26}, {"ruleId": "1922", "severity": 1, "message": "2401", "line": 854, "column": 5, "nodeType": "1924", "endLine": 854, "endColumn": 64, "suggestions": "2402"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 854, "column": 6, "nodeType": "2127", "endLine": 854, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2403", "line": 863, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 863, "endColumn": 21}, {"ruleId": "1922", "severity": 1, "message": "2404", "line": 877, "column": 5, "nodeType": "1924", "endLine": 877, "endColumn": 47, "suggestions": "2405"}, {"ruleId": "1912", "severity": 1, "message": "2403", "line": 886, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 886, "endColumn": 21}, {"ruleId": "1922", "severity": 1, "message": "2404", "line": 899, "column": 5, "nodeType": "1924", "endLine": 899, "endColumn": 47, "suggestions": "2406"}, {"ruleId": "1922", "severity": 1, "message": "2407", "line": 1293, "column": 17, "nodeType": "1914", "endLine": 1293, "endColumn": 32}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 1499, "column": 43, "nodeType": "2073", "messageId": "2074", "endLine": 1499, "endColumn": 45}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 1504, "column": 78, "nodeType": "2073", "messageId": "2074", "endLine": 1504, "endColumn": 80}, {"ruleId": "1912", "severity": 1, "message": "2408", "line": 1, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 5, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 5}, {"ruleId": "1912", "severity": 1, "message": "2409", "line": 6, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2410", "line": 10, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2411", "line": 12, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 13, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2412", "line": 17, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2256", "line": 19, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2413", "line": 34, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2274", "line": 34, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 44}, {"ruleId": "2414", "severity": 1, "message": "2415", "line": 96, "column": 2, "nodeType": "2416", "messageId": "2417", "endLine": 112, "endColumn": 4}, {"ruleId": "1912", "severity": 1, "message": "2418", "line": 133, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 133, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2419", "line": 136, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 136, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 137, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 137, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 138, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 138, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2420", "line": 140, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 140, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2304", "line": 141, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 141, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2421", "line": 142, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 142, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2422", "line": 145, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 145, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2423", "line": 146, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 146, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2424", "line": 147, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 147, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2425", "line": 148, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 148, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2426", "line": 149, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 149, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2427", "line": 150, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 150, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2314", "line": 151, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 151, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2313", "line": 152, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 152, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2312", "line": 153, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 153, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2428", "line": 156, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 156, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2429", "line": 159, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 159, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2430", "line": 160, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 160, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2431", "line": 161, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 161, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2233", "line": 162, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 162, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2196", "line": 163, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 163, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2432", "line": 166, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 166, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2433", "line": 167, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 167, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2392", "line": 169, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 169, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2434", "line": 174, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 174, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2050", "line": 175, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 175, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2435", "line": 177, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 177, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2436", "line": 186, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 186, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2437", "line": 186, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 186, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2438", "line": 188, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 188, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2439", "line": 188, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 188, "endColumn": 44}, {"ruleId": "2440", "severity": 1, "message": "2441", "line": 350, "column": 5, "nodeType": "2442", "messageId": "2443", "endLine": 350, "endColumn": 52}, {"ruleId": "1912", "severity": 1, "message": "2444", "line": 505, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 505, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2445", "line": 575, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 575, "endColumn": 21}, {"ruleId": "1922", "severity": 1, "message": "2446", "line": 742, "column": 5, "nodeType": "1924", "endLine": 742, "endColumn": 100, "suggestions": "2447"}, {"ruleId": "1922", "severity": 1, "message": "2448", "line": 760, "column": 5, "nodeType": "1924", "endLine": 760, "endColumn": 83, "suggestions": "2449"}, {"ruleId": "1912", "severity": 1, "message": "2450", "line": 940, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 940, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2451", "line": 947, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 947, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2452", "line": 5, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2453", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2454", "line": 6, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 7, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2240", "line": 26, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2201", "line": 29, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2455", "line": 32, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2456", "line": 145, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 145, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2457", "line": 146, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 146, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2452", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2458", "line": 1, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2459", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2231", "line": 8, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2237", "line": 9, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2232", "line": 10, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2201", "line": 11, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2241", "line": 14, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2460", "line": 20, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2458", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2461", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 2, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 2, "column": 39, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2410", "line": 2, "column": 44, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 58}, {"ruleId": "1912", "severity": 1, "message": "2186", "line": 2, "column": 60, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 73}, {"ruleId": "1912", "severity": 1, "message": "2290", "line": 2, "column": 74, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 84}, {"ruleId": "1912", "severity": 1, "message": "2462", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2463", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2231", "line": 98, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 98, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2237", "line": 99, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 99, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2232", "line": 100, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 100, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2041", "line": 101, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2233", "line": 102, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 102, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2234", "line": 103, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 103, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2058", "line": 104, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2059", "line": 105, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2275", "line": 110, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 110, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 113, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 113, "endColumn": 29}, {"ruleId": "1922", "severity": 1, "message": "2464", "line": 172, "column": 12, "nodeType": "1924", "endLine": 172, "endColumn": 35, "suggestions": "2465"}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 1, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "1920", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2177", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1969", "line": 7, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2466", "line": 43, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 34}, {"ruleId": "1922", "severity": 1, "message": "2467", "line": 63, "column": 21, "nodeType": "2468", "endLine": 63, "endColumn": 111}, {"ruleId": "1912", "severity": 1, "message": "2469", "line": 2, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "2470", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "1919", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2471", "line": 11, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2472", "line": 1, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2473", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "1958", "line": 1, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2473", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2474", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 40}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2290", "line": 2, "column": 50, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 60}, {"ruleId": "1912", "severity": 1, "message": "2475", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2475", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2462", "line": 29, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2222", "line": 34, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2434", "line": 67, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 67, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 75, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 75, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2477", "line": 77, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 77, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2478", "line": 80, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2479", "line": 81, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2480", "line": 100, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 100, "endColumn": 22}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 110, "column": 34, "nodeType": "2073", "messageId": "2074", "endLine": 110, "endColumn": 36}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 158, "column": 44, "nodeType": "2073", "messageId": "2074", "endLine": 158, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2481", "line": 177, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 177, "endColumn": 21}, {"ruleId": "1922", "severity": 1, "message": "2482", "line": 306, "column": 5, "nodeType": "1924", "endLine": 306, "endColumn": 50, "suggestions": "2483"}, {"ruleId": "1922", "severity": 1, "message": "2482", "line": 322, "column": 5, "nodeType": "1924", "endLine": 322, "endColumn": 18, "suggestions": "2484"}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 483, "column": 48, "nodeType": "2073", "messageId": "2074", "endLine": 483, "endColumn": 50}, {"ruleId": "1912", "severity": 1, "message": "2452", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2485", "line": 59, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2486", "line": 61, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 5, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2487", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2261", "line": 85, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2488", "line": 98, "column": 6, "nodeType": "1924", "endLine": 98, "endColumn": 8, "suggestions": "2489"}, {"ruleId": "1922", "severity": 1, "message": "2490", "line": 121, "column": 6, "nodeType": "1924", "endLine": 121, "endColumn": 32, "suggestions": "2491"}, {"ruleId": "1922", "severity": 1, "message": "2267", "line": 125, "column": 6, "nodeType": "1924", "endLine": 125, "endColumn": 40, "suggestions": "2492"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 125, "column": 7, "nodeType": "2073", "endLine": 125, "endColumn": 39}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 125, "column": 35, "nodeType": "2073", "messageId": "2074", "endLine": 125, "endColumn": 37}, {"ruleId": "1922", "severity": 1, "message": "2493", "line": 148, "column": 6, "nodeType": "1924", "endLine": 148, "endColumn": 33, "suggestions": "2494"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 148, "column": 7, "nodeType": "2078", "endLine": 148, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2495", "line": 156, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 156, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 2, "column": 80, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 2, "column": 105, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 111}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 2, "column": 113, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 121}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2499", "line": 2, "column": 168, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 180}, {"ruleId": "1912", "severity": 1, "message": "2500", "line": 2, "column": 182, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 199}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 4, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 4, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 4, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 13, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 14, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 15, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 16, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 17, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2509", "line": 24, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2510", "line": 25, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2511", "line": 26, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2512", "line": 27, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2513", "line": 28, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 28, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2514", "line": 29, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2515", "line": 30, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 30, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2516", "line": 40, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2517", "line": 41, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 41, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2518", "line": 43, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2519", "line": 45, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2269", "line": 47, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2520", "line": 94, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 94, "endColumn": 19}, {"ruleId": "1922", "severity": 1, "message": "2521", "line": 125, "column": 5, "nodeType": "1924", "endLine": 125, "endColumn": 7, "suggestions": "2522"}, {"ruleId": "1912", "severity": 1, "message": "2523", "line": 145, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 145, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2524", "line": 162, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 162, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2525", "line": 165, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 165, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2526", "line": 170, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 170, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2527", "line": 211, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 211, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2528", "line": 214, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 214, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2529", "line": 227, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 227, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2530", "line": 228, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 228, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2531", "line": 228, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 228, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2532", "line": 229, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 229, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2533", "line": 229, "column": 20, "nodeType": "1914", "messageId": "1915", "endLine": 229, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2006", "line": 245, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 245, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2007", "line": 245, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 245, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2534", "line": 247, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 247, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2535", "line": 261, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 261, "endColumn": 36}, {"ruleId": "1922", "severity": 1, "message": "2536", "line": 281, "column": 4, "nodeType": "1924", "endLine": 281, "endColumn": 6, "suggestions": "2537"}, {"ruleId": "1912", "severity": 1, "message": "2535", "line": 334, "column": 12, "nodeType": "1914", "messageId": "1915", "endLine": 334, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2538", "line": 347, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 347, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2539", "line": 347, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 347, "endColumn": 45}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 2, "column": 80, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 2, "column": 105, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 111}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 2, "column": 113, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 121}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 2, "column": 168, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 175}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 4, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 4, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 4, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 8, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 9, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 10, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 11, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 12, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2540", "line": 13, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2541", "line": 14, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2542", "line": 15, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2543", "line": 22, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2544", "line": 31, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2252", "line": 32, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 32, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2516", "line": 35, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 35, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2517", "line": 36, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 36, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2518", "line": 38, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 38, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2545", "line": 39, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2546", "line": 40, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2547", "line": 42, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 42, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2548", "line": 43, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2549", "line": 44, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2550", "line": 45, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2551", "line": 46, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2552", "line": 47, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2553", "line": 48, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2554", "line": 49, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2555", "line": 50, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2556", "line": 51, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 58, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2557", "line": 60, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2520", "line": 75, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 75, "endColumn": 19}, {"ruleId": "1922", "severity": 1, "message": "2493", "line": 87, "column": 5, "nodeType": "1924", "endLine": 87, "endColumn": 45, "suggestions": "2558"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 87, "column": 6, "nodeType": "2127", "endLine": 87, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2559", "line": 106, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2560", "line": 106, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2561", "line": 107, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 107, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2562", "line": 107, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 107, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2563", "line": 108, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 108, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2195", "line": 109, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 109, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2564", "line": 109, "column": 18, "nodeType": "1914", "messageId": "1915", "endLine": 109, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2565", "line": 110, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 110, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2524", "line": 115, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 115, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2526", "line": 119, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 119, "endColumn": 25}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 134, "column": 11, "nodeType": "2073", "messageId": "2074", "endLine": 134, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2567", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 22}, {"ruleId": "1922", "severity": 1, "message": "2568", "line": 283, "column": 8, "nodeType": "1924", "endLine": 283, "endColumn": 10, "suggestions": "2569"}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 44, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 53}, {"ruleId": "1912", "severity": 1, "message": "2570", "line": 4, "column": 46, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 65}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 4, "column": 67, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 75}, {"ruleId": "1912", "severity": 1, "message": "2462", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2571", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2231", "line": 30, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 30, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2572", "line": 31, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 34, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2573", "line": 44, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2574", "line": 45, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2575", "line": 46, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2576", "line": 47, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2577", "line": 51, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2578", "line": 51, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2579", "line": 52, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2580", "line": 53, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2581", "line": 56, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 56, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2582", "line": 57, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 57, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2583", "line": 57, "column": 20, "nodeType": "1914", "messageId": "1915", "endLine": 57, "endColumn": 32}, {"ruleId": "1922", "severity": 1, "message": "2584", "line": 65, "column": 5, "nodeType": "1924", "endLine": 65, "endColumn": 7, "suggestions": "2585"}, {"ruleId": "1912", "severity": 1, "message": "2586", "line": 93, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 93, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2587", "line": 97, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 97, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2588", "line": 124, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 124, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2589", "line": 132, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 132, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2590", "line": 136, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 136, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2591", "line": 150, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 150, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2592", "line": 153, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 153, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2593", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2594", "line": 5, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2595", "line": 10, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 6, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 9, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 10, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 11, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 12, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2596", "line": 17, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2324", "line": 33, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 33, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2597", "line": 35, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 35, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2413", "line": 36, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 36, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2274", "line": 37, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 37, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2598", "line": 38, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 38, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2197", "line": 40, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 46, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2599", "line": 53, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2600", "line": 54, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2601", "line": 55, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 55, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2602", "line": 84, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2603", "line": 88, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 88, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2604", "line": 93, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 93, "endColumn": 33}, {"ruleId": "1922", "severity": 1, "message": "2605", "line": 193, "column": 5, "nodeType": "1924", "endLine": 193, "endColumn": 30, "suggestions": "2606"}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2499", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "2500", "line": 2, "column": 41, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 58}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 72, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 88}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 90, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 96}, {"ruleId": "1912", "severity": 1, "message": "2607", "line": 9, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 3, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 5}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 4, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 9, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2593", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2594", "line": 5, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2608", "line": 9, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2609", "line": 9, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2595", "line": 9, "column": 32, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 41}, {"ruleId": "1912", "severity": 1, "message": "2610", "line": 9, "column": 43, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 51}, {"ruleId": "1912", "severity": 1, "message": "2611", "line": 9, "column": 53, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 63}, {"ruleId": "1912", "severity": 1, "message": "2612", "line": 9, "column": 65, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 77}, {"ruleId": "1912", "severity": 1, "message": "2613", "line": 9, "column": 79, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 90}, {"ruleId": "1912", "severity": 1, "message": "2614", "line": 9, "column": 92, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 102}, {"ruleId": "1912", "severity": 1, "message": "2615", "line": 9, "column": 104, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 116}, {"ruleId": "1912", "severity": 1, "message": "2616", "line": 9, "column": 118, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 129}, {"ruleId": "1912", "severity": 1, "message": "2617", "line": 9, "column": 131, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2618", "line": 15, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 16, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2421", "line": 17, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2619", "line": 18, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 19, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2420", "line": 22, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2620", "line": 23, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2621", "line": 24, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2622", "line": 25, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2623", "line": 26, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2624", "line": 27, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2625", "line": 28, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 28, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2626", "line": 29, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 33, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 33, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2520", "line": 61, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2603", "line": 81, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2627", "line": 82, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2628", "line": 4, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2629", "line": 4, "column": 32, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 45}, {"ruleId": "1912", "severity": 1, "message": "2630", "line": 10, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2631", "line": 65, "column": 12, "nodeType": "1914", "messageId": "1915", "endLine": 65, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2632", "line": 65, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 65, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2633", "line": 78, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 78, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2634", "line": 78, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 78, "endColumn": 39}, {"ruleId": "1922", "severity": 1, "message": "2635", "line": 120, "column": 6, "nodeType": "1924", "endLine": 120, "endColumn": 8, "suggestions": "2636"}, {"ruleId": "1912", "severity": 1, "message": "2637", "line": 157, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 157, "endColumn": 32}, {"ruleId": "1912", "severity": 1, "message": "2638", "line": 280, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 280, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2639", "line": 296, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 296, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2640", "line": 461, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 461, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2641", "line": 462, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 462, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2642", "line": 467, "column": 3, "nodeType": "1924", "endLine": 467, "endColumn": 5, "suggestions": "2643"}, {"ruleId": "1912", "severity": 1, "message": "2644", "line": 2, "column": 14, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2645", "line": 16, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2557", "line": 19, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2646", "line": 22, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 20}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 43, "column": 100, "nodeType": "2073", "messageId": "2074", "endLine": 43, "endColumn": 102}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 3, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 3, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 3, "column": 80, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 3, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 3, "column": 105, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 111}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 3, "column": 113, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 121}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 3, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 3, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 3, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 5, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 5, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 5, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 8, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 9, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 10, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 11, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2516", "line": 19, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2518", "line": 22, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2647", "line": 24, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2648", "line": 25, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2649", "line": 26, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2650", "line": 27, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 31, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2520", "line": 36, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 36, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2651", "line": 104, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2652", "line": 105, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 40}, {"ruleId": "1912", "severity": 1, "message": "2523", "line": 120, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 120, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2524", "line": 154, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 154, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2653", "line": 157, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 157, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2654", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2409", "line": 4, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 8, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2655", "line": 15, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2656", "line": 15, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2657", "line": 15, "column": 46, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 60}, {"ruleId": "1912", "severity": 1, "message": "2658", "line": 15, "column": 62, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2659", "line": 15, "column": 80, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 96}, {"ruleId": "1912", "severity": 1, "message": "2660", "line": 17, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2661", "line": 17, "column": 35, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2187", "line": 17, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 55}, {"ruleId": "1912", "severity": 1, "message": "2662", "line": 22, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2663", "line": 58, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2664", "line": 66, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 66, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2421", "line": 72, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 72, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2420", "line": 73, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 73, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2621", "line": 74, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 74, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 75, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 75, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2665", "line": 76, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 76, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2428", "line": 77, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 77, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2666", "line": 78, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 78, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2429", "line": 79, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 79, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2424", "line": 80, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2667", "line": 81, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2623", "line": 82, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2196", "line": 83, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 83, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2191", "line": 84, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2668", "line": 87, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 87, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2669", "line": 89, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 89, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2197", "line": 91, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 91, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2202", "line": 93, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 93, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2670", "line": 166, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 166, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2671", "line": 169, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 169, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2672", "line": 179, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 179, "endColumn": 21}, {"ruleId": "1922", "severity": 1, "message": "2673", "line": 332, "column": 5, "nodeType": "1924", "endLine": 332, "endColumn": 18, "suggestions": "2674"}, {"ruleId": "1912", "severity": 1, "message": "2431", "line": 599, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 599, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2274", "line": 599, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 599, "endColumn": 60}, {"ruleId": "1912", "severity": 1, "message": "2233", "line": 599, "column": 62, "nodeType": "1914", "messageId": "1915", "endLine": 599, "endColumn": 67}, {"ruleId": "1912", "severity": 1, "message": "2196", "line": 599, "column": 69, "nodeType": "1914", "messageId": "1915", "endLine": 599, "endColumn": 85}, {"ruleId": "1922", "severity": 1, "message": "2675", "line": 927, "column": 3, "nodeType": "1924", "endLine": 927, "endColumn": 19, "suggestions": "2676"}, {"ruleId": "1912", "severity": 1, "message": "2677", "line": 1000, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 1000, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2678", "line": 1009, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 1009, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2362", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2038", "line": 11, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2039", "line": 12, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 13, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2368", "line": 21, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2369", "line": 22, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2370", "line": 22, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 44}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 82, "column": 22, "nodeType": "2073", "messageId": "2074", "endLine": 82, "endColumn": 24}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 82, "column": 53, "nodeType": "2073", "messageId": "2074", "endLine": 82, "endColumn": 55}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 85, "column": 36, "nodeType": "2073", "messageId": "2074", "endLine": 85, "endColumn": 38}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 85, "column": 63, "nodeType": "2073", "messageId": "2074", "endLine": 85, "endColumn": 65}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 88, "column": 36, "nodeType": "2073", "messageId": "2074", "endLine": 88, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2372", "line": 95, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 95, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2373", "line": 96, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 96, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2374", "line": 99, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 99, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2375", "line": 100, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 100, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2376", "line": 108, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 108, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2377", "line": 128, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 128, "endColumn": 16}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 268, "column": 45, "nodeType": "2073", "messageId": "2074", "endLine": 268, "endColumn": 47}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 268, "column": 104, "nodeType": "2073", "messageId": "2074", "endLine": 268, "endColumn": 106}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 3, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 3, "column": 56, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 63}, {"ruleId": "1912", "severity": 1, "message": "2198", "line": 13, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2304", "line": 14, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2305", "line": 15, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2011", "line": 16, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2013", "line": 18, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2014", "line": 19, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2015", "line": 20, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2016", "line": 21, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2306", "line": 22, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 23, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2308", "line": 24, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 25, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2679", "line": 37, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 37, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2346", "line": 39, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2680", "line": 41, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 41, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2681", "line": 45, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2682", "line": 49, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2683", "line": 49, "column": 25, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2684", "line": 50, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2685", "line": 50, "column": 21, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2686", "line": 51, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2687", "line": 51, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2688", "line": 52, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2689", "line": 52, "column": 30, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 52}, {"ruleId": "1912", "severity": 1, "message": "2690", "line": 53, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2691", "line": 53, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 56, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 65}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 2, "column": 67, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 75}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 2, "column": 77, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 83}, {"ruleId": "1912", "severity": 1, "message": "2570", "line": 13, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2692", "line": 47, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 48}, {"ruleId": "1912", "severity": 1, "message": "2386", "line": 59, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 39}, {"ruleId": "1912", "severity": 1, "message": "2693", "line": 68, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 68, "endColumn": 41}, {"ruleId": "1912", "severity": 1, "message": "2694", "line": 74, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 74, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2452", "line": 1, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 4, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 4, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 4, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2695", "line": 21, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2047", "line": 22, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2696", "line": 24, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2043", "line": 25, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2697", "line": 26, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2698", "line": 27, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2331", "line": 83, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 83, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2699", "line": 3, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2700", "line": 4, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2701", "line": 5, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2702", "line": 6, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2461", "line": 9, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2179", "line": 17, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2180", "line": 18, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 19, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2182", "line": 20, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 7}, {"ruleId": "1912", "severity": 1, "message": "2183", "line": 23, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2184", "line": 24, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2185", "line": 25, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 26, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 26, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2363", "line": 43, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2703", "line": 44, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2704", "line": 50, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2705", "line": 51, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2706", "line": 53, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2707", "line": 54, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2708", "line": 55, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 55, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2709", "line": 56, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 56, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2231", "line": 59, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2572", "line": 60, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2192", "line": 62, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 62, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2193", "line": 68, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 68, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2194", "line": 69, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 69, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2195", "line": 75, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 75, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2710", "line": 77, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 77, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2038", "line": 80, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2039", "line": 81, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 81, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2037", "line": 82, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2711", "line": 83, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 83, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2712", "line": 84, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2713", "line": 85, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2208", "line": 87, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 87, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2206", "line": 89, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 89, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2210", "line": 91, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 91, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2714", "line": 92, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2203", "line": 94, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 94, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 101, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2306", "line": 101, "column": 22, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 102, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 102, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2308", "line": 102, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 102, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2715", "line": 103, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 103, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2716", "line": 104, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2717", "line": 105, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2718", "line": 105, "column": 14, "nodeType": "1914", "messageId": "1915", "endLine": 105, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2204", "line": 106, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2719", "line": 106, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2390", "line": 107, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 107, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2720", "line": 107, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 107, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2601", "line": 118, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 118, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2721", "line": 118, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 118, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2722", "line": 125, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 125, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2723", "line": 125, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 125, "endColumn": 44}, {"ruleId": "1922", "severity": 1, "message": "2724", "line": 148, "column": 5, "nodeType": "1924", "endLine": 148, "endColumn": 60, "suggestions": "2725"}, {"ruleId": "1912", "severity": 1, "message": "2726", "line": 151, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 151, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2727", "line": 163, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 163, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2728", "line": 167, "column": 5, "nodeType": "1924", "endLine": 167, "endColumn": 60, "suggestions": "2729"}, {"ruleId": "1912", "severity": 1, "message": "2730", "line": 169, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 169, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2211", "line": 203, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 203, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2212", "line": 207, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 207, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 79}, {"ruleId": "1912", "severity": 1, "message": "1971", "line": 15, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2731", "line": 18, "column": 48, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 76}, {"ruleId": "1912", "severity": 1, "message": "2732", "line": 18, "column": 78, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 85}, {"ruleId": "1912", "severity": 1, "message": "2297", "line": 20, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2733", "line": 52, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2734", "line": 54, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2735", "line": 59, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2669", "line": 59, "column": 18, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2736", "line": 60, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2006", "line": 61, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2007", "line": 61, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2730", "line": 85, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2526", "line": 92, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 92, "endColumn": 25}, {"ruleId": "1922", "severity": 1, "message": "2737", "line": 183, "column": 5, "nodeType": "1924", "endLine": 183, "endColumn": 52, "suggestions": "2738"}, {"ruleId": "1922", "severity": 1, "message": "2077", "line": 183, "column": 6, "nodeType": "2127", "endLine": 183, "endColumn": 51}, {"ruleId": "1912", "severity": 1, "message": "2411", "line": 11, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2362", "line": 14, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 14, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2739", "line": 67, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 67, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2740", "line": 180, "column": 86, "nodeType": "1914", "messageId": "1915", "endLine": 180, "endColumn": 101}, {"ruleId": "1912", "severity": 1, "message": "2429", "line": 184, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 184, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2741", "line": 563, "column": 5, "nodeType": "1924", "endLine": 563, "endColumn": 40, "suggestions": "2742"}, {"ruleId": "1922", "severity": 1, "message": "2743", "line": 586, "column": 6, "nodeType": "1924", "endLine": 586, "endColumn": 42, "suggestions": "2744"}, {"ruleId": "1922", "severity": 1, "message": "2745", "line": 600, "column": 6, "nodeType": "1924", "endLine": 600, "endColumn": 50, "suggestions": "2746"}, {"ruleId": "1922", "severity": 1, "message": "2747", "line": 877, "column": 5, "nodeType": "1924", "endLine": 877, "endColumn": 160, "suggestions": "2748"}, {"ruleId": "1922", "severity": 1, "message": "2749", "line": 945, "column": 5, "nodeType": "1924", "endLine": 945, "endColumn": 110, "suggestions": "2750"}, {"ruleId": "1922", "severity": 1, "message": "2751", "line": 993, "column": 5, "nodeType": "1924", "endLine": 993, "endColumn": 34, "suggestions": "2752"}, {"ruleId": "1922", "severity": 1, "message": "2753", "line": 1011, "column": 5, "nodeType": "1924", "endLine": 1011, "endColumn": 34, "suggestions": "2754"}, {"ruleId": "1922", "severity": 1, "message": "2753", "line": 1025, "column": 5, "nodeType": "1924", "endLine": 1025, "endColumn": 34, "suggestions": "2755"}, {"ruleId": "1922", "severity": 1, "message": "2753", "line": 1028, "column": 5, "nodeType": "1924", "endLine": 1028, "endColumn": 40, "suggestions": "2756"}, {"ruleId": "1912", "severity": 1, "message": "2757", "line": 1238, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1238, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2758", "line": 1241, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 1241, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 92, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 102}, {"ruleId": "1912", "severity": 1, "message": "2759", "line": 76, "column": 19, "nodeType": "1914", "messageId": "1915", "endLine": 76, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2458", "line": 1, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 37}, {"ruleId": "1912", "severity": 1, "message": "2452", "line": 1, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2282", "line": 1, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 59}, {"ruleId": "1912", "severity": 1, "message": "2408", "line": 1, "column": 61, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 67}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 2, "column": 56, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 62}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2286", "line": 2, "column": 80, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 2, "column": 105, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 111}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 2, "column": 113, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 121}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2462", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2177", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 4, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 4, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 4, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 7, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 8, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 9, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 10, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 11, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2540", "line": 12, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2760", "line": 19, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 2, "column": 64, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 78}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2184", "line": 2, "column": 177, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 193}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 4, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 4, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 4, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 5, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 7, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 8, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 9, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 10, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 11, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2510", "line": 27, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2511", "line": 28, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 28, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2512", "line": 29, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 29, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2513", "line": 30, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 30, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2516", "line": 38, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 38, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2517", "line": 39, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2518", "line": 41, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 41, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2545", "line": 42, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 42, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2546", "line": 43, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2761", "line": 44, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2547", "line": 45, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2549", "line": 47, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2550", "line": 48, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 33}, {"ruleId": "1912", "severity": 1, "message": "2551", "line": 49, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2553", "line": 51, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2554", "line": 52, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2555", "line": 53, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 53, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2556", "line": 54, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 58, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2762", "line": 152, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 152, "endColumn": 40}, {"ruleId": "1912", "severity": 1, "message": "2763", "line": 153, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 153, "endColumn": 41}, {"ruleId": "1912", "severity": 1, "message": "2764", "line": 154, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 154, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2006", "line": 156, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 156, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2765", "line": 185, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 185, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2524", "line": 220, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 220, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2525", "line": 223, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 223, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2526", "line": 228, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 228, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2111", "line": 245, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 245, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2559", "line": 249, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 249, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2565", "line": 254, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 254, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2766", "line": 263, "column": 6, "nodeType": "1924", "endLine": 263, "endColumn": 8, "suggestions": "2767"}, {"ruleId": "1912", "severity": 1, "message": "2768", "line": 298, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 298, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2461", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2290", "line": 2, "column": 36, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 48, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 57}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 2, "column": 59, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 67}, {"ruleId": "1912", "severity": 1, "message": "2152", "line": 2, "column": 69, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 75}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 2, "column": 77, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 84}, {"ruleId": "1912", "severity": 1, "message": "2769", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2770", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2771", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2772", "line": 9, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2496", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2408", "line": 1, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 55}, {"ruleId": "1912", "severity": 1, "message": "2773", "line": 1, "column": 69, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 80}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 2, "column": 93, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 103}, {"ruleId": "1912", "severity": 1, "message": "2497", "line": 2, "column": 123, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 140}, {"ruleId": "1912", "severity": 1, "message": "2181", "line": 2, "column": 142, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 158}, {"ruleId": "1912", "severity": 1, "message": "2498", "line": 2, "column": 160, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 166}, {"ruleId": "1912", "severity": 1, "message": "2409", "line": 2, "column": 195, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 212}, {"ruleId": "1912", "severity": 1, "message": "2501", "line": 5, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 49}, {"ruleId": "1912", "severity": 1, "message": "2502", "line": 5, "column": 51, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 71}, {"ruleId": "1912", "severity": 1, "message": "2503", "line": 5, "column": 73, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 91}, {"ruleId": "1912", "severity": 1, "message": "2504", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2509", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2510", "line": 9, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2511", "line": 10, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2512", "line": 11, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2513", "line": 12, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2514", "line": 13, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2505", "line": 15, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2506", "line": 16, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2507", "line": 17, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 17, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2508", "line": 18, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 19, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2770", "line": 34, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2774", "line": 44, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2516", "line": 45, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2517", "line": 46, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2518", "line": 48, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2545", "line": 49, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2546", "line": 50, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2761", "line": 51, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2547", "line": 52, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 52, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2549", "line": 54, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2550", "line": 55, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 55, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2551", "line": 56, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 56, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2553", "line": 58, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2554", "line": 59, "column": 6, "nodeType": "1914", "messageId": "1915", "endLine": 59, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2555", "line": 60, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2556", "line": 61, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2775", "line": 63, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 63, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2476", "line": 66, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 66, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2762", "line": 89, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 89, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2763", "line": 90, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 90, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "2764", "line": 91, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 91, "endColumn": 46}, {"ruleId": "1922", "severity": 1, "message": "2776", "line": 112, "column": 5, "nodeType": "1924", "endLine": 112, "endColumn": 52, "suggestions": "2777"}, {"ruleId": "1912", "severity": 1, "message": "2524", "line": 117, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 117, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2525", "line": 120, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 120, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2526", "line": 125, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 125, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2778", "line": 135, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 135, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2111", "line": 175, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 175, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2779", "line": 183, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 183, "endColumn": 20}, {"ruleId": "1922", "severity": 1, "message": "2766", "line": 188, "column": 4, "nodeType": "1924", "endLine": 188, "endColumn": 6, "suggestions": "2780"}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 193, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 193, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 194, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 194, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 194, "column": 39, "nodeType": "2073", "messageId": "2074", "endLine": 194, "endColumn": 41}, {"ruleId": "2071", "severity": 1, "message": "2072", "line": 213, "column": 19, "nodeType": "2073", "messageId": "2074", "endLine": 213, "endColumn": 21}, {"ruleId": "2071", "severity": 1, "message": "2093", "line": 226, "column": 20, "nodeType": "2073", "messageId": "2074", "endLine": 226, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2781", "line": 279, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 279, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2006", "line": 307, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 307, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2765", "line": 334, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 334, "endColumn": 23}, {"ruleId": "1922", "severity": 1, "message": "2782", "line": 371, "column": 4, "nodeType": "1924", "endLine": 371, "endColumn": 6, "suggestions": "2783"}, {"ruleId": "1912", "severity": 1, "message": "2771", "line": 5, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2772", "line": 6, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2784", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2785", "line": 27, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 27, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2786", "line": 34, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 34, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2485", "line": 56, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 56, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2486", "line": 58, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2787", "line": 80, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 80, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2788", "line": 82, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2789", "line": 82, "column": 23, "nodeType": "1914", "messageId": "1915", "endLine": 82, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "2790", "line": 133, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 133, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2791", "line": 290, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 290, "endColumn": 28}, {"ruleId": "1922", "severity": 1, "message": "2792", "line": 344, "column": 5, "nodeType": "1924", "endLine": 344, "endColumn": 22, "suggestions": "2793"}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 32, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 44, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 53}, {"ruleId": "1912", "severity": 1, "message": "2570", "line": 4, "column": 46, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 65}, {"ruleId": "1912", "severity": 1, "message": "1951", "line": 4, "column": 67, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 75}, {"ruleId": "1912", "severity": 1, "message": "2794", "line": 8, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2663", "line": 16, "column": 13, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2795", "line": 31, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2321", "line": 33, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 33, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2796", "line": 43, "column": 6, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 34}, {"ruleId": "1912", "severity": 1, "message": "2797", "line": 85, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 85, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2591", "line": 95, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 95, "endColumn": 26}, {"ruleId": "1912", "severity": 1, "message": "2654", "line": 1, "column": 58, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 67}, {"ruleId": "1912", "severity": 1, "message": "1927", "line": 1, "column": 75, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 82}, {"ruleId": "1912", "severity": 1, "message": "2461", "line": 2, "column": 15, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2221", "line": 2, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "2662", "line": 4, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2178", "line": 5, "column": 41, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 53}, {"ruleId": "1912", "severity": 1, "message": "2660", "line": 6, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2187", "line": 6, "column": 16, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2798", "line": 6, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 29}, {"ruleId": "1912", "severity": 1, "message": "2799", "line": 6, "column": 31, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2343", "line": 6, "column": 37, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "2661", "line": 6, "column": 49, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 61}, {"ruleId": "1912", "severity": 1, "message": "2462", "line": 7, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2770", "line": 8, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2800", "line": 39, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2801", "line": 40, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2802", "line": 42, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 42, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2349", "line": 43, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2803", "line": 44, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2348", "line": 45, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 45, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2347", "line": 46, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2804", "line": 47, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 47, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2346", "line": 49, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2805", "line": 50, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 50, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2806", "line": 51, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 51, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2418", "line": 54, "column": 4, "nodeType": "1914", "messageId": "1915", "endLine": 54, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2272", "line": 58, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2273", "line": 58, "column": 20, "nodeType": "1914", "messageId": "1915", "endLine": 58, "endColumn": 31}, {"ruleId": "1912", "severity": 1, "message": "2807", "line": 60, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2808", "line": 60, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 60, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "2809", "line": 76, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 76, "endColumn": 30}, {"ruleId": "1912", "severity": 1, "message": "2810", "line": 84, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 84, "endColumn": 29}, {"ruleId": "1922", "severity": 1, "message": "2811", "line": 117, "column": 6, "nodeType": "1924", "endLine": 117, "endColumn": 35, "suggestions": "2812"}, {"ruleId": "1912", "severity": 1, "message": "2813", "line": 3, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 3, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2509", "line": 6, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2566", "line": 2, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2284", "line": 2, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "2307", "line": 9, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2306", "line": 9, "column": 22, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "2028", "line": 10, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2308", "line": 10, "column": 26, "nodeType": "1914", "messageId": "1915", "endLine": 10, "endColumn": 44}, {"ruleId": "1912", "severity": 1, "message": "2716", "line": 12, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2736", "line": 12, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 12, "endColumn": 46}, {"ruleId": "1912", "severity": 1, "message": "2717", "line": 13, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2718", "line": 13, "column": 14, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2390", "line": 15, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2601", "line": 16, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2814", "line": 28, "column": 8, "nodeType": "1914", "messageId": "1915", "endLine": 28, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2183", "line": 2, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2815", "line": 20, "column": 24, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 35}, {"ruleId": "1912", "severity": 1, "message": "2318", "line": 42, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 42, "endColumn": 21}, {"ruleId": "1912", "severity": 1, "message": "2413", "line": 43, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 43, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2199", "line": 44, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 44, "endColumn": 28}, {"ruleId": "1912", "severity": 1, "message": "2816", "line": 46, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 46, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2275", "line": 48, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 17}, {"ruleId": "1912", "severity": 1, "message": "2321", "line": 49, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 49, "endColumn": 20}, {"ruleId": "1912", "severity": 1, "message": "2302", "line": 61, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2303", "line": 61, "column": 45, "nodeType": "1914", "messageId": "1915", "endLine": 61, "endColumn": 62}, {"ruleId": "1912", "severity": 1, "message": "2817", "line": 101, "column": 11, "nodeType": "1914", "messageId": "1915", "endLine": 101, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2818", "line": 104, "column": 12, "nodeType": "1914", "messageId": "1915", "endLine": 104, "endColumn": 26}, {"ruleId": "1922", "severity": 1, "message": "2490", "line": 129, "column": 5, "nodeType": "1924", "endLine": 129, "endColumn": 155, "suggestions": "2819"}, {"ruleId": "1912", "severity": 1, "message": "2469", "line": 1, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 41}, {"ruleId": "1912", "severity": 1, "message": "2820", "line": 22, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 23}, {"ruleId": "1912", "severity": 1, "message": "2821", "line": 31, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 31, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2822", "line": 40, "column": 7, "nodeType": "1914", "messageId": "1915", "endLine": 40, "endColumn": 25}, {"ruleId": "1912", "severity": 1, "message": "2823", "line": 5, "column": 3, "nodeType": "1914", "messageId": "1915", "endLine": 5, "endColumn": 24}, {"ruleId": "1922", "severity": 1, "message": "2824", "line": 121, "column": 6, "nodeType": "1924", "endLine": 121, "endColumn": 26, "suggestions": "2825"}, {"ruleId": "1912", "severity": 1, "message": "2826", "line": 39, "column": 9, "nodeType": "1914", "messageId": "1915", "endLine": 39, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2282", "line": 1, "column": 17, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 27}, {"ruleId": "1912", "severity": 1, "message": "2458", "line": 1, "column": 28, "nodeType": "1914", "messageId": "1915", "endLine": 1, "endColumn": 36}, {"ruleId": "1912", "severity": 1, "message": "1931", "line": 2, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 2, "endColumn": 22}, {"ruleId": "1912", "severity": 1, "message": "2283", "line": 4, "column": 10, "nodeType": "1914", "messageId": "1915", "endLine": 4, "endColumn": 24}, {"ruleId": "1912", "severity": 1, "message": "2699", "line": 6, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 6, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2700", "line": 7, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 7, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "1953", "line": 8, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 8, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2827", "line": 9, "column": 2, "nodeType": "1914", "messageId": "1915", "endLine": 9, "endColumn": 19}, {"ruleId": "1912", "severity": 1, "message": "2227", "line": 11, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 11, "endColumn": 8}, {"ruleId": "1912", "severity": 1, "message": "2285", "line": 13, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 13, "endColumn": 14}, {"ruleId": "1912", "severity": 1, "message": "2185", "line": 15, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 15, "endColumn": 16}, {"ruleId": "1912", "severity": 1, "message": "2186", "line": 16, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 16, "endColumn": 18}, {"ruleId": "1912", "severity": 1, "message": "2287", "line": 18, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 18, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2288", "line": 19, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 19, "endColumn": 11}, {"ruleId": "1912", "severity": 1, "message": "2289", "line": 20, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 20, "endColumn": 13}, {"ruleId": "1912", "severity": 1, "message": "2290", "line": 21, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 21, "endColumn": 15}, {"ruleId": "1912", "severity": 1, "message": "2291", "line": 22, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 22, "endColumn": 12}, {"ruleId": "1912", "severity": 1, "message": "2292", "line": 23, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 23, "endColumn": 10}, {"ruleId": "1912", "severity": 1, "message": "2293", "line": 24, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 24, "endColumn": 9}, {"ruleId": "1912", "severity": 1, "message": "2828", "line": 25, "column": 5, "nodeType": "1914", "messageId": "1915", "endLine": 25, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", "ArrayExpression", ["2829"], "'signIn' is assigned a value but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", ["2830"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2831"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2832"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2833"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2834"], "React Hook useEffect has a missing dependency: 'setIsAIGuidePersisted'. Either include it or remove the dependency array.", ["2835"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2836"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'resetALTKeywordForNewTooltip', 'setElementSelected', 'setIsALTKeywordEnabled', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2837"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2838"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2839"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2840"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2841"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2842"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2843"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2844"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2845"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCreateWithAI', 'setCurrentGuideId', 'setIsAIGuidePersisted', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2846"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2847"], "'getAccountIdForUpdate' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2848", "2849"], "'selectedStepTitle' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "no-self-assign", "'state.overlayEnabled' is assigned to itself.", "selfAssignment", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2850"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2851"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2852"], "'Typography' is defined but never used.", "'useAuth' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'Box' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2853"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2854"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2855"], "'handleEnableAI' is assigned a value but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "VariableDeclarator", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2856"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'useContext' is defined but never used.", "'AccountContext' is defined but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'designicon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2857"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2858"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2859"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2860"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2861"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2862"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2863"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "'getElementPosition' is assigned a value but never used.", "'calculateBestPosition' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculatePopupPosition'. Either include it or remove the dependency array.", ["2864"], ["2865"], ["2866"], ["2867"], ["2868"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2869"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2870"], ["2871"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyCustomCursor', 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2872"], "React Hook useEffect has a missing dependency: 'applyCustomCursor'. Either include it or remove the dependency array.", ["2873"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2874"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setActiveMenu' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2875"], ["2876"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2877"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2878"], ["2879"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2880"], "'toggleItemCompletion' is assigned a value but never used.", "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2881"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2882"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", "'accountId' is assigned a value but never used.", ["2883"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "React Hook useMemo has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2884"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2885"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2886"], "'OverlaySettingsProps' is defined but never used.", "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2887"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2888"], "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2889"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2890"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2891"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2892"], "'handlePositionClick' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2893"], "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2894"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2895"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2896"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2897"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2898"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2899"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2900"], ["2901"], ["2902"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'guideStatus' is assigned a value but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2903"], "'handleMenuScroll' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2904"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2905"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2906"], "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2907"], "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["2908"], "'FolderIcon' is defined but never used.", "'handleColorChange' is assigned a value but never used.", "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2909"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["2910"], "'orgId' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'TextareaAutosize' is defined but never used.", {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2937", "fix": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2941", "fix": "2942"}, {"desc": "2943", "fix": "2944"}, {"desc": "2945", "fix": "2946"}, {"desc": "2947", "fix": "2948"}, {"messageId": "2949", "fix": "2950", "desc": "2951"}, {"messageId": "2952", "fix": "2953", "desc": "2954"}, {"desc": "2955", "fix": "2956"}, {"desc": "2957", "fix": "2958"}, {"desc": "2959", "fix": "2960"}, {"desc": "2961", "fix": "2962"}, {"desc": "2963", "fix": "2964"}, {"desc": "2965", "fix": "2966"}, {"desc": "2967", "fix": "2968"}, {"desc": "2969", "fix": "2970"}, {"desc": "2971", "fix": "2972"}, {"desc": "2973", "fix": "2974"}, {"desc": "2975", "fix": "2976"}, {"desc": "2977", "fix": "2978"}, {"desc": "2979", "fix": "2980"}, {"desc": "2981", "fix": "2982"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2991", "fix": "2992"}, {"desc": "2993", "fix": "2994"}, {"desc": "2995", "fix": "2996"}, {"desc": "2997", "fix": "2998"}, {"desc": "2999", "fix": "3000"}, {"desc": "3001", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3005", "fix": "3006"}, {"desc": "3007", "fix": "3008"}, {"desc": "3009", "fix": "3010"}, {"desc": "3011", "fix": "3012"}, {"desc": "2967", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3014", "fix": "3020"}, {"desc": "3021", "fix": "3022"}, {"desc": "3023", "fix": "3024"}, {"desc": "3025", "fix": "3026"}, {"desc": "3027", "fix": "3028"}, {"desc": "3029", "fix": "3030"}, {"desc": "3031", "fix": "3032"}, {"desc": "3033", "fix": "3034"}, {"desc": "3035", "fix": "3036"}, {"desc": "3037", "fix": "3038"}, {"desc": "3039", "fix": "3040"}, {"desc": "3041", "fix": "3042"}, {"desc": "3043", "fix": "3044"}, {"desc": "3045", "fix": "3046"}, {"desc": "3047", "fix": "3048"}, {"desc": "3049", "fix": "3050"}, {"desc": "3051", "fix": "3052"}, {"desc": "3053", "fix": "3054"}, {"desc": "3053", "fix": "3055"}, {"desc": "3056", "fix": "3057"}, {"desc": "3058", "fix": "3059"}, {"desc": "3060", "fix": "3061"}, {"desc": "3058", "fix": "3062"}, {"desc": "3063", "fix": "3064"}, {"desc": "3065", "fix": "3066"}, {"desc": "3067", "fix": "3068"}, {"desc": "3069", "fix": "3070"}, {"desc": "3071", "fix": "3072"}, "Update the dependencies array to be: [loggedOut]", {"range": "3073", "text": "3074"}, "Update the dependencies array to be: []", {"range": "3075", "text": "3076"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3077", "text": "3078"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3079", "text": "3080"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3081", "text": "3082"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3083", "text": "3084"}, "Update the dependencies array to be: [initialState, setIsAIGuidePersisted]", {"range": "3085", "text": "3086"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3087", "text": "3088"}, "Update the dependencies array to be: [handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", {"range": "3089", "text": "3090"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3091", "text": "3092"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3093", "text": "3094"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3095", "text": "3096"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3097", "text": "3098"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3099", "text": "3100"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3101", "text": "3102"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3103", "text": "3104"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3105", "text": "3106"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3107", "text": "3108"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3109", "text": "3110"}, "removeEscape", {"range": "3111", "text": "3112"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3113", "text": "3114"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3115", "text": "3116"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3117", "text": "3118"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3119", "text": "3120"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3121", "text": "3122"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3123", "text": "3124"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3125", "text": "3126"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3127", "text": "3128"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3129", "text": "3130"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3131", "text": "3132"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3133", "text": "3134"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3135", "text": "3136"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3137", "text": "3138"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3139", "text": "3140"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3141", "text": "3142"}, "Update the dependencies array to be: [xpath, toolTipGuideMetaData, selectedTemplateTour, currentStep, calculatePopupPosition]", {"range": "3143", "text": "3144"}, "Update the dependencies array to be: [guideStep, currentStep, toolTipGuideMetaData, selectedTemplateTour, calculatePopupPosition]", {"range": "3145", "text": "3146"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour, calculatePopupPosition]", {"range": "3147", "text": "3148"}, "Update the dependencies array to be: [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton, selectedTemplateTour, currentStep, calculatePopupPosition]", {"range": "3149", "text": "3150"}, "Update the dependencies array to be: [xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour, currentStep, calculatePopupPosition]", {"range": "3151", "text": "3152"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3153", "text": "3154"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3155", "text": "3156"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3157", "text": "3158"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", {"range": "3159", "text": "3160"}, "Update the dependencies array to be: [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", {"range": "3161", "text": "3162"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3163", "text": "3164"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3165", "text": "3166"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3167", "text": "3168"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3169", "text": "3170"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3171", "text": "3172"}, {"range": "3173", "text": "3128"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3174", "text": "3175"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3176", "text": "3177"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3178", "text": "3179"}, {"range": "3180", "text": "3175"}, "Update the dependencies array to be: [handlePaste]", {"range": "3181", "text": "3182"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3183", "text": "3184"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3185", "text": "3186"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3187", "text": "3188"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3189", "text": "3190"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3191", "text": "3192"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3193", "text": "3194"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3195", "text": "3196"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3197", "text": "3198"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3199", "text": "3200"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3201", "text": "3202"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3203", "text": "3204"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3205", "text": "3206"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3207", "text": "3208"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3209", "text": "3210"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3211", "text": "3212"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3213", "text": "3214"}, {"range": "3215", "text": "3214"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3216", "text": "3217"}, "Update the dependencies array to be: [fetchData]", {"range": "3218", "text": "3219"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3220", "text": "3221"}, {"range": "3222", "text": "3219"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3223", "text": "3224"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3225", "text": "3226"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3227", "text": "3228"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3229", "text": "3230"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3231", "text": "3232"}, [4501, 4503], "[loggedOut]", [18294, 18316], "[]", [26734, 26775], "[fetchGuideDetails, hotspot, hotspotClicked]", [27257, 27270], "[designPopup, setDesignPopup]", [29994, 30141], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30619, 30979], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [31347, 31361], "[initialState, setIsAIGuidePersisted]", [35755, 35789], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [71540, 71604], "[handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", [87621, 87654], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [102253, 102267], "[createWithAI, setIsUnSavedChanges, stepCreation]", [125572, 125600], "[isLoggedIn, organizationId, userType]", [131546, 131698], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [161336, 161393], "[currentGuide?.GuideStep, currentStep]", [166650, 166667], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [172599, 172632], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [173342, 173443], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [178873, 178885], "[isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [179305, 179378], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [201895, 201896], "", [201895, 201895], "\\", [15982, 16037], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17411, 17466], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [17911, 17913], "[selectedActions.value, targetURL]", [2467, 2469], "[isExtensionClosed, setIsExtensionClosed]", [3134, 3157], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3501, 3544], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [5960, 5993], "[checkpointslistData]", [5334, 5374], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10258, 10293], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", [16482, 16546], "[xpath, toolTipGuideMetaData, selectedTemplateTour, currentStep, calculatePopupPosition]", [17972, 18040], "[guideStep, currentStep, toolTipGuideMetaData, selectedTemplateTour, calculatePopupPosition]", [25776, 25905], "[textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour, calculatePopupPosition]", [26980, 27109], "[hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton, selectedTemplateTour, currentStep, calculatePopupPosition]", [28282, 28359], "[xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour, currentStep, calculatePopupPosition]", [31180, 31239], "[currentStep, guideStep, setOpenTooltip]", [32093, 32135], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [32927, 32969], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [25153, 25248], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", [25832, 25910], "[isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [9051, 9096], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9402, 9415], "[fetchAnnouncements, searchQuery]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [13207, 13209], "[handlePaste]", [2651, 2653], "[setButtonProperty]", [6784, 6809], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [9769, 9782], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26416, 26432], "[handleFocus, isRtlDirection]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [34487, 34516], "[currentStepData, currentStepIndex, handleNext]", [34993, 35022], "[currentStepData, currentUrl, updateTargetAndPosition]", [35434, 35463], [35522, 35557], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [3811, 3840], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4267, 4417], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]"]