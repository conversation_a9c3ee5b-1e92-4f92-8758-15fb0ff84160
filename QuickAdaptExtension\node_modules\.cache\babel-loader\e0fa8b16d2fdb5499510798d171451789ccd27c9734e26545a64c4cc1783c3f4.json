{"ast": null, "code": "import React,{useEffect,useState,useRef}from\"react\";import{Box,Button,IconButton,LinearProgress,MobileStepper,Popover,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HotspotPreview=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide31,_savedGuideData$Guide32,_savedGuideData$Guide33;let{anchorEl,guideStep,title,text,imageUrl,onClose,onPrevious,onContinue,videoUrl,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData,hotspotProperties,handleHotspotHover,handleHotspotClick,isHotspotPopupOpen,showHotspotenduser}=_ref;const{setCurrentStep,selectedTemplate,toolTipGuideMetaData,elementSelected,axisData,tooltipXaxis,tooltipYaxis,setOpenTooltip,openTooltip,pulseAnimationsH,hotspotGuideMetaData,selectedTemplateTour,selectedOption,ProgressColor}=useDrawerStore(state=>state);const[targetElement,setTargetElement]=useState(null);// State to track if the popover should be shown\n// State for popup visibility is managed through openTooltip\nconst[popupPosition,setPopupPosition]=useState(null);const[hotspotSize,setHotspotSize]=useState(30);// Track hotspot size for dynamic popup positioning\nconst contentRef=useRef(null);const buttonContainerRef=useRef(null);let hotspot;const getElementByXPath=xpath=>{const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;// Return parent if it's a text node\n}else{return null;}};let xpath;if(savedGuideData)xpath=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:_savedGuideData$Guide2.ElementPath;const getElementPosition=xpath=>{const element=getElementByXPath(xpath||\"\");if(element){const rect=element.getBoundingClientRect();return{top:rect.top,//+ window.scrollY + yOffset, // Adjust for vertical scroll\nleft:rect.left// + window.scrollX + xOffset, // Adjust for horizontal scroll\n};}return null;};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);// Function to get estimated popup dimensions based on actual content (enhanced to match TooltipPreview)\nconst getEstimatedPopupDimensions=()=>{// Try to get actual dimensions from content if available (similar to TooltipPreview approach)\nif(contentRef.current){const contentRect=contentRef.current.getBoundingClientRect();const actualWidth=Math.max(contentRect.width,50);// Minimum width\nconst actualHeight=Math.max(contentRect.height+100,150);// Add padding for buttons/progress\n// Apply width constraints similar to TooltipPreview's approach\nconst widthStyling=getWidthStyling();let constrainedWidth=actualWidth;// Check if using auto width (like TooltipPreview's 'auto !important')\nif(widthStyling.width.includes('auto')){// For auto-width, respect maxWidth constraint (like TooltipPreview's maxWidth approach)\nconst maxWidthValue=widthStyling.maxWidth.replace(/[^\\d]/g,'');// Extract numeric value\nconst maxWidth=parseInt(maxWidthValue)||300;constrainedWidth=Math.min(actualWidth,maxWidth);}else{// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\nconst fixedWidthValue=widthStyling.width.replace(/[^\\d]/g,'');// Extract numeric value\nconstrainedWidth=parseInt(fixedWidthValue)||actualWidth;}return{width:constrainedWidth,height:actualHeight};}// Fallback estimation when content ref is not available (enhanced to match TooltipPreview)\nconst widthStyling=getWidthStyling();let estimatedWidth=300;// Default fallback\n// Check if using auto width (like TooltipPreview's 'auto !important')\nif(widthStyling.width.includes('auto')){// For auto-width, estimate based on content type and respect maxWidth (like TooltipPreview)\nconst maxWidthValue=widthStyling.maxWidth.replace(/[^\\d]/g,'');// Extract numeric value\nconst maxWidth=parseInt(maxWidthValue)||300;// Enhanced content-based width estimation (similar to TooltipPreview's approach)\nlet contentBasedWidth=250;// Base estimate\n// Adjust based on content types present (similar to TooltipPreview's content analysis)\nif(textFieldProperties&&textFieldProperties.length>0){// Estimate text width based on content length (enhanced calculation)\nconst totalTextLength=textFieldProperties.reduce((sum,field)=>sum+(field.Text?field.Text.length:0),0);const avgTextLength=totalTextLength/textFieldProperties.length;// Use character-based width estimation similar to TooltipPreview\ncontentBasedWidth=Math.min(Math.max(avgTextLength*7.5,200),maxWidth);}if(imageProperties&&imageProperties.length>0){// Images typically need more width (similar to TooltipPreview's image handling)\ncontentBasedWidth=Math.max(contentBasedWidth,280);}if(customButton&&customButton.length>0){// Buttons may need additional width (enhanced button width calculation)\nconst buttonWidth=customButton.length*85;// Slightly larger estimate for better fit\ncontentBasedWidth=Math.max(contentBasedWidth,Math.min(buttonWidth+50,maxWidth));}estimatedWidth=Math.min(contentBasedWidth,maxWidth);}else{// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\nconst fixedWidthValue=widthStyling.width.replace(/[^\\d]/g,'');// Extract numeric value\nestimatedWidth=parseInt(fixedWidthValue)||300;}const estimatedHeight=250;// Standard height for all content types\nreturn{width:estimatedWidth,height:estimatedHeight};};// Function to calculate best position based on available space (similar to TooltipPreview)\nconst calculateBestPosition=elementRect=>{const viewportWidth=window.innerWidth;const viewportHeight=window.innerHeight;const spaceTop=elementRect.top;const spaceBottom=viewportHeight-elementRect.bottom;const spaceLeft=elementRect.left;const spaceRight=viewportWidth-elementRect.right;const maxSpace=Math.max(spaceTop,spaceBottom,spaceLeft,spaceRight);if(maxSpace===spaceTop)return\"top\";if(maxSpace===spaceBottom)return\"bottom\";if(maxSpace===spaceLeft)return\"left\";return\"right\";};// Function to calculate smart popup position that stays within viewport (enhanced with TooltipPreview logic)\nconst calculatePopupPosition=(elementRect,hotspotSize,xOffset,yOffset)=>{const hotspotLeft=elementRect.x+xOffset;const hotspotTop=elementRect.y+yOffset;// Get viewport dimensions\nconst viewportWidth=window.innerWidth;const viewportHeight=window.innerHeight;// Get estimated popup dimensions with enhanced width calculation\nconst{width:popupWidth,height:popupHeight}=getEstimatedPopupDimensions();// Viewport margin to ensure tooltip doesn't touch edges (similar to TooltipPreview's preventOverflow)\nconst VIEWPORT_MARGIN=20;// Gap between hotspot beacon and tooltip (similar to TooltipPreview's arrow spacing)\nconst TOOLTIP_GAP=12;// Smaller gap for tooltips positioned above hotspots (closer to beacon for better visual connection)\nconst TOOLTIP_GAP_ABOVE=8;// Handle very small viewports - reduce margins if popup is too large\nconst availableWidth=viewportWidth-VIEWPORT_MARGIN*2;const availableHeight=viewportHeight-VIEWPORT_MARGIN*2;const effectiveMargin=Math.min(VIEWPORT_MARGIN,Math.max(5,Math.min((availableWidth-popupWidth)/2,(availableHeight-popupHeight)/2)));// Calculate hotspot beacon bounds (the visual indicator/marker)\nconst hotspotRight=hotspotLeft+hotspotSize;const hotspotBottom=hotspotTop+hotspotSize;const hotspotCenterX=hotspotLeft+hotspotSize/2;const hotspotCenterY=hotspotTop+hotspotSize/2;// Define possible positions in order of preference (enhanced with center-based positioning)\nconst positions=[// Primary: bottom-center (positioned below hotspot beacon center)\n{name:'bottom-center',left:hotspotCenterX-popupWidth/2,top:hotspotBottom+TOOLTIP_GAP},// Secondary: top-center (positioned above hotspot beacon center)\n{name:'top-center',left:hotspotCenterX-popupWidth/2,top:hotspotTop-popupHeight-TOOLTIP_GAP_ABOVE},// Tertiary: right-center (positioned to the right of hotspot beacon center)\n{name:'right-center',left:hotspotRight+TOOLTIP_GAP,top:hotspotCenterY-popupHeight/2},// Quaternary: left-center (positioned to the left of hotspot beacon center)\n{name:'left-center',left:hotspotLeft-popupWidth-TOOLTIP_GAP,top:hotspotCenterY-popupHeight/2},// Fallback: bottom-right (original default)\n{name:'bottom-right',left:hotspotRight+TOOLTIP_GAP,top:hotspotBottom+TOOLTIP_GAP},// Fallback: bottom-left\n{name:'bottom-left',left:hotspotLeft-popupWidth-TOOLTIP_GAP,top:hotspotBottom+TOOLTIP_GAP}];// Function to check if a position fits within viewport\nconst isPositionValid=pos=>{const right=pos.left+popupWidth;const bottom=pos.top+popupHeight;return pos.left>=effectiveMargin&&pos.top>=effectiveMargin&&right<=viewportWidth-effectiveMargin&&bottom<=viewportHeight-effectiveMargin;};// Find the first valid position\nlet selectedPosition=positions.find(pos=>isPositionValid(pos));// If no position fits perfectly, use the default and adjust to fit\nif(!selectedPosition){selectedPosition=positions[0];// Default: bottom-right\n// Adjust horizontally if needed\nif(selectedPosition.left+popupWidth>viewportWidth-effectiveMargin){selectedPosition.left=viewportWidth-popupWidth-effectiveMargin;}if(selectedPosition.left<effectiveMargin){selectedPosition.left=effectiveMargin;}// Adjust vertically if needed\nif(selectedPosition.top+popupHeight>viewportHeight-effectiveMargin){selectedPosition.top=viewportHeight-popupHeight-effectiveMargin;}if(selectedPosition.top<effectiveMargin){selectedPosition.top=effectiveMargin;}}// Debug logging (can be removed in production)\nif(process.env.NODE_ENV==='development'){console.log('Smart positioning:',{hotspotPosition:{left:hotspotLeft,top:hotspotTop},hotspotSize:hotspotSize,popupDimensions:{width:popupWidth,height:popupHeight},viewport:{width:viewportWidth,height:viewportHeight},selectedPosition:selectedPosition.name,finalPosition:{left:selectedPosition.left,top:selectedPosition.top}});}return{top:selectedPosition.top+window.scrollY,left:selectedPosition.left+window.scrollX};};useEffect(()=>{const element=getElementByXPath(xpath);if(element){const rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,// Account for scrolling\nleft:rect.left+window.scrollX});}},[xpath]);useEffect(()=>{if(typeof window!==undefined){const position=getElementPosition(xpath||\"\");if(position){setPopupPosition(position);}}},[xpath]);useEffect(()=>{const element=getElementByXPath(xpath);// setTargetElement(element);\nif(element){}},[savedGuideData]);useEffect(()=>{var _guideStep;const element=getElementByXPath(guideStep===null||guideStep===void 0?void 0:(_guideStep=guideStep[currentStep-1])===null||_guideStep===void 0?void 0:_guideStep.ElementPath);setTargetElement(element);if(element){element.style.backgroundColor=\"red !important\";// Update popup position when target element changes\nconst rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,left:rect.left+window.scrollX});}},[guideStep,currentStep]);// Hotspot styles are applied directly in the applyHotspotStyles function\n// State for overlay value\nconst[,setOverlayValue]=useState(false);const handleContinue=()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){setCurrentStep(currentStep+1);onContinue();renderNextPopup(currentStep<totalSteps);}}else{setCurrentStep(currentStep+1);const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}}};const renderNextPopup=shouldRenderNextPopup=>{var _savedGuideData$Guide3,_savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6,_savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11,_savedGuideData$Guide12;return shouldRenderNextPopup?/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:isHotspotPopupOpen,showHotspotenduser:showHotspotenduser,handleHotspotHover:handleHotspotHover,handleHotspotClick:handleHotspotClick,anchorEl:anchorEl,savedGuideData:savedGuideData,guideStep:guideStep,onClose:onClose,onPrevious:handlePrevious,onContinue:handleContinue,title:title,text:text,imageUrl:imageUrl,currentStep:currentStep+1,totalSteps:totalSteps,onDontShowAgain:onDontShowAgain,progress:progress,textFieldProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide3=savedGuideData.GuideStep)===null||_savedGuideData$Guide3===void 0?void 0:(_savedGuideData$Guide4=_savedGuideData$Guide3[currentStep])===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4.TextFieldProperties,imageProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5[currentStep])===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.ImageProperties,customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[currentStep])===null||_savedGuideData$Guide8===void 0?void 0:(_savedGuideData$Guide9=_savedGuideData$Guide8.ButtonSection)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide10===void 0?void 0:_savedGuideData$Guide10.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:modalProperties,canvasProperties:canvasProperties,htmlSnippet:htmlSnippet,OverlayValue:OverlayValue,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide11=savedGuideData.GuideStep)===null||_savedGuideData$Guide11===void 0?void 0:(_savedGuideData$Guide12=_savedGuideData$Guide11[currentStep-1])===null||_savedGuideData$Guide12===void 0?void 0:_savedGuideData$Guide12.Hotspot)||{}}):null;};const handlePrevious=()=>{if(currentStep>1){setCurrentStep(currentStep-1);onPrevious();}};useEffect(()=>{if(OverlayValue){setOverlayValue(true);}else{setOverlayValue(false);}},[OverlayValue]);// Image fit is used directly in the component\nconst getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};const{anchorOrigin,transformOrigin}=getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};// Image styles are applied directly in the component\nconst renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(_match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Function to check if canvas width has been modified from default\nconst isCanvasWidthModified=()=>{var _canvasProperties$Wid;const defaultWidth=\"300\";// Default width without \"px\"\nconst currentWidth=(canvasProperties===null||canvasProperties===void 0?void 0:(_canvasProperties$Wid=canvasProperties.Width)===null||_canvasProperties$Wid===void 0?void 0:_canvasProperties$Wid.replace(\"px\",\"\"))||defaultWidth;return currentWidth!==defaultWidth;};// Function to get width styling based on user preferences (enhanced to match TooltipPreview approach)\nconst getWidthStyling=()=>{// Check if user has modified canvas width from default\nconst hasCustomWidth=isCanvasWidthModified();if(hasCustomWidth&&canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width){// User-specified width: use exact width with !important, matching TooltipPreview's approach\nconst userWidth=canvasProperties.Width.includes('px')?canvasProperties.Width:`${canvasProperties.Width}px`;return{width:`${userWidth} !important`,// Use !important like TooltipPreview\nmaxWidth:`${userWidth} !important`,// Override the default limit with !important\nminWidth:'unset'// Remove any minimum width constraints\n};}else{// Default behavior: auto-adjust with content-based sizing, exactly like TooltipPreview\n// TooltipPreview uses: width: 'auto !important', maxWidth: canvasStyle?.Width || \"300px\"\nreturn{width:'auto !important',// Match TooltipPreview's exact approach\nmaxWidth:'300px !important',// Default maximum width constraint with !important\nminWidth:'unset'// Remove minimum width constraints for small content\n};}};// Update popup position when content changes (width is now calculated dynamically)\nuseEffect(()=>{// Recalculate popup position when content changes since width affects positioning\nif(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData===void 0?void 0:_toolTipGuideMetaData.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[textFieldProperties,imageProperties,customButton,currentStep,xpath,hotspotSize,toolTipGuideMetaData]);// Recalculate popup position when hotspot size changes or content dimensions change\nuseEffect(()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData2;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData2=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[hotspotSize,xpath,toolTipGuideMetaData,textFieldProperties,imageProperties,customButton]);// Recalculate popup position on window resize\nuseEffect(()=>{const handleResize=()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData3;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData3=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData3===void 0?void 0:_toolTipGuideMetaData3.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[xpath,hotspotSize,toolTipGuideMetaData]);const groupedButtons=customButton.reduce((acc,button)=>{const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});const widthStyling=getWidthStyling();const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Radius)||\"4px\",borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"black\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",// Apply padding similar to TooltipPreview approach\npadding:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding)||\"10px\",// Apply width styling with enhanced approach matching TooltipPreview\n...widthStyling,// Add box shadow similar to TooltipPreview for consistency\nboxShadow:\"0px 4px 12px rgba(0, 0, 0, 0.2)\",// Add border handling similar to TooltipPreview\nborder:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.BorderSize&&(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)!==\"0px\"?`${canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize} solid ${(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"transparent\"}`:\"none\"};const sectionHeight=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.SectionHeight)||\"auto\";const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"Previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide13,_savedGuideData$Guide14;// Reset to the first step\nsetCurrentStep(1);// If there's a specific URL for the first step, navigate to it\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide13=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide13!==void 0&&(_savedGuideData$Guide14=_savedGuideData$Guide13[0])!==null&&_savedGuideData$Guide14!==void 0&&_savedGuideData$Guide14.ElementPath){const firstStepElement=getElementByXPath(savedGuideData.GuideStep[0].ElementPath);if(firstStepElement){firstStepElement.scrollIntoView({behavior:'smooth'});}}}}setOverlayValue(false);};useEffect(()=>{var _guideStep2,_guideStep2$Hotspot;if(guideStep!==null&&guideStep!==void 0&&(_guideStep2=guideStep[currentStep-1])!==null&&_guideStep2!==void 0&&(_guideStep2$Hotspot=_guideStep2.Hotspot)!==null&&_guideStep2$Hotspot!==void 0&&_guideStep2$Hotspot.ShowByDefault){// Show tooltip by default\nsetOpenTooltip(true);}},[guideStep===null||guideStep===void 0?void 0:guideStep[currentStep-1],currentStep,setOpenTooltip]);// Add effect to handle isHotspotPopupOpen prop changes\nuseEffect(()=>{if(isHotspotPopupOpen){var _toolTipGuideMetaData4,_toolTipGuideMetaData5,_savedGuideData$Guide15,_savedGuideData$Guide16,_savedGuideData$Guide17,_savedGuideData$Guide18;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData4=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData4!==void 0&&_toolTipGuideMetaData4.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData5=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData5===void 0?void 0:_toolTipGuideMetaData5.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide15=savedGuideData.GuideStep)===null||_savedGuideData$Guide15===void 0?void 0:(_savedGuideData$Guide16=_savedGuideData$Guide15[currentStep-1])===null||_savedGuideData$Guide16===void 0?void 0:_savedGuideData$Guide16.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide17=savedGuideData.GuideStep)===null||_savedGuideData$Guide17===void 0?void 0:(_savedGuideData$Guide18=_savedGuideData$Guide17[0])===null||_savedGuideData$Guide18===void 0?void 0:_savedGuideData$Guide18.Hotspot;// Only show tooltip by default if ShowByDefault is true\n// For \"Hovering Hotspot\", we'll wait for the hover event\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[isHotspotPopupOpen,toolTipGuideMetaData]);// Add effect to handle showHotspotenduser prop changes\nuseEffect(()=>{if(showHotspotenduser){var _toolTipGuideMetaData6,_toolTipGuideMetaData7,_savedGuideData$Guide19,_savedGuideData$Guide20,_savedGuideData$Guide21,_savedGuideData$Guide22;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData6=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData6!==void 0&&_toolTipGuideMetaData6.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData7=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData7===void 0?void 0:_toolTipGuideMetaData7.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide19=savedGuideData.GuideStep)===null||_savedGuideData$Guide19===void 0?void 0:(_savedGuideData$Guide20=_savedGuideData$Guide19[currentStep-1])===null||_savedGuideData$Guide20===void 0?void 0:_savedGuideData$Guide20.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide21=savedGuideData.GuideStep)===null||_savedGuideData$Guide21===void 0?void 0:(_savedGuideData$Guide22=_savedGuideData$Guide21[0])===null||_savedGuideData$Guide22===void 0?void 0:_savedGuideData$Guide22.Hotspot;// Only show tooltip by default if ShowByDefault is true\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[showHotspotenduser,toolTipGuideMetaData]);// Add a global click handler to detect clicks outside the hotspot to close the tooltip\nuseEffect(()=>{const handleGlobalClick=e=>{const hotspotElement=document.getElementById(\"hotspotBlink\");// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\nif(hotspotElement&&hotspotElement.contains(e.target)){return;}// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on clicks outside anymore\n};document.addEventListener(\"click\",handleGlobalClick);return()=>{document.removeEventListener(\"click\",handleGlobalClick);};},[toolTipGuideMetaData]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);// We no longer need the persistent monitoring effect since we want the tooltip\n// to close when the mouse leaves the hotspot\nfunction getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const getCanvasPosition=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";switch(position){case\"bottom-left\":return{top:\"auto !important\"};case\"bottom-right\":return{top:\"auto !important\"};case\"bottom-center\":return{top:\"auto !important\"};case\"center-center\":return{top:\"25% !important\"};case\"left-center\":return{top:imageUrl===\"\"?\"40% !important\":\"20% !important\"};case\"right-center\":return{top:\"10% !important\"};case\"top-left\":return{top:\"10% !important\"};case\"top-right\":return{top:\"10% !important\"};case\"top-center\":return{top:\"9% !important\"};default:return{top:\"25% !important\"};}};// function to get the correct property value based on tour vs normal hotspot\nconst getHotspotProperty=(propName,hotspotPropData,hotspotData)=>{if(selectedTemplateTour===\"Hotspot\"){// For tour hotspots, use saved data first, fallback to metadata\nswitch(propName){case'PulseAnimation':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.PulseAnimation)!==undefined?hotspotData.PulseAnimation:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.PulseAnimation;case'StopAnimation':// Always use stopAnimationUponInteraction for consistency\nreturn(hotspotData===null||hotspotData===void 0?void 0:hotspotData.stopAnimationUponInteraction)!==undefined?hotspotData.stopAnimationUponInteraction:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;case'ShowUpon':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowUpon)!==undefined?hotspotData.ShowUpon:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon;case'ShowByDefault':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowByDefault)!==undefined?hotspotData.ShowByDefault:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowByDefault;default:return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}}else{// For normal hotspots, use metadata\nif(propName==='StopAnimation'){return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;}return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}};const applyHotspotStyles=(hotspot,hotspotPropData,hotspotData,left,top)=>{hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;// Default size if not provided\nhotspot.style.height=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;hotspot.style.backgroundColor=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Color;hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"auto !important\";// Increased z-index\nhotspot.style.transition=\"none\";hotspot.style.pointerEvents=\"auto\";// Ensure clicks are registered\nhotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";hotspot.appendChild(textSpan);}// Apply animation class if needed\n// Track if pulse has been stopped by hover\nconst pulseAnimationEnabled=getHotspotProperty('PulseAnimation',hotspotPropData,hotspotData);const shouldPulse=selectedTemplateTour===\"Hotspot\"?pulseAnimationEnabled!==false&&!hotspot._pulseStopped:hotspotPropData&&pulseAnimationsH&&!hotspot._pulseStopped;if(shouldPulse){hotspot.classList.add(\"pulse-animation\");hotspot.classList.remove(\"pulse-animation-removed\");}else{hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");}// Ensure the hotspot is visible and clickable\nhotspot.style.display=\"flex\";hotspot.style.pointerEvents=\"auto\";// No need for separate animation control functions here\n// Animation will be controlled directly in the event handlers\n// Set initial state of openTooltip based on ShowByDefault and ShowUpon\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{// If not showing by default, only show based on interaction type\n//setOpenTooltip(false);\n}// Only clone and replace if the hotspot doesn't have event listeners already\n// This prevents losing the _pulseStopped state unnecessarily\nif(!hotspot.hasAttribute('data-listeners-attached')){const newHotspot=hotspot.cloneNode(true);// Copy the _pulseStopped property if it exists\nif(hotspot._pulseStopped!==undefined){newHotspot._pulseStopped=hotspot._pulseStopped;}if(hotspot.parentNode){hotspot.parentNode.replaceChild(newHotspot,hotspot);hotspot=newHotspot;}}// Ensure pointer events are enabled\nhotspot.style.pointerEvents=\"auto\";// Define combined event handlers that handle both animation and tooltip\nconst showUpon=getHotspotProperty('ShowUpon',hotspotPropData,hotspotData);const handleHover=e=>{e.stopPropagation();console.log(\"Hover detected on hotspot\");// Show tooltip if ShowUpon is \"Hovering Hotspot\"\nif(showUpon===\"Hovering Hotspot\"){// Set openTooltip to true when hovering\nsetOpenTooltip(true);// Call the passed hover handler if it exists\nif(typeof handleHotspotHover===\"function\"){handleHotspotHover();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};const handleMouseOut=e=>{e.stopPropagation();// Hide tooltip when mouse leaves the hotspot\n// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showUpon===\"Hovering Hotspot\"&&!showByDefault){// setOpenTooltip(false);\n}};const handleClick=e=>{e.stopPropagation();console.log(\"Click detected on hotspot\");// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\nif(showUpon===\"Clicking Hotspot\"||!showUpon){// Toggle the tooltip state\nsetOpenTooltip(!openTooltip);// Call the passed click handler if it exists\nif(typeof handleHotspotClick===\"function\"){handleHotspotClick();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};// Add appropriate event listeners based on ShowUpon property\nif(!hotspot.hasAttribute('data-listeners-attached')){if(showUpon===\"Hovering Hotspot\"){// For hover interaction\nhotspot.addEventListener(\"mouseover\",handleHover);hotspot.addEventListener(\"mouseout\",handleMouseOut);// Also add click handler for better user experience\nhotspot.addEventListener(\"click\",handleClick);}else{// For click interaction (default)\nhotspot.addEventListener(\"click\",handleClick);}// Mark that listeners have been attached\nhotspot.setAttribute('data-listeners-attached','true');}};useEffect(()=>{let element;let steps;const fetchGuideDetails=async()=>{try{var _savedGuideData$Guide23,_savedGuideData$Guide24,_steps,_steps$;//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\nsteps=(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideStep)||[];// For tour hotspots, use the current step's element path\nconst elementPath=selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide23=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide23!==void 0&&(_savedGuideData$Guide24=_savedGuideData$Guide23[currentStep-1])!==null&&_savedGuideData$Guide24!==void 0&&_savedGuideData$Guide24.ElementPath?savedGuideData.GuideStep[currentStep-1].ElementPath:((_steps=steps)===null||_steps===void 0?void 0:(_steps$=_steps[0])===null||_steps$===void 0?void 0:_steps$.ElementPath)||\"\";element=getElementByXPath(elementPath||\"\");setTargetElement(element);if(element){// element.style.outline = \"2px solid red\";\n}// Check if this is a hotspot scenario (normal or tour)\nconst isHotspotScenario=selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"||title===\"Hotspot\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Hotspot\";if(isHotspotScenario){var _toolTipGuideMetaData8,_toolTipGuideMetaData9,_hotspotPropData,_hotspotPropData2,_hotspotPropData3,_hotspotPropData4;// Get hotspot properties - prioritize tour data for tour hotspots\nlet hotspotPropData;let hotspotData;if(selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData8=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData8!==void 0&&_toolTipGuideMetaData8.hotspots){var _savedGuideData$Guide25,_savedGuideData$Guide26;// Tour hotspot - use current step metadata\nhotspotPropData=toolTipGuideMetaData[currentStep-1].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide25=savedGuideData.GuideStep)===null||_savedGuideData$Guide25===void 0?void 0:(_savedGuideData$Guide26=_savedGuideData$Guide25[currentStep-1])===null||_savedGuideData$Guide26===void 0?void 0:_savedGuideData$Guide26.Hotspot;}else if(toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData9=toolTipGuideMetaData[0])!==null&&_toolTipGuideMetaData9!==void 0&&_toolTipGuideMetaData9.hotspots){var _savedGuideData$Guide27,_savedGuideData$Guide28;// Normal hotspot - use first metadata entry\nhotspotPropData=toolTipGuideMetaData[0].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide27=savedGuideData.GuideStep)===null||_savedGuideData$Guide27===void 0?void 0:(_savedGuideData$Guide28=_savedGuideData$Guide27[0])===null||_savedGuideData$Guide28===void 0?void 0:_savedGuideData$Guide28.Hotspot;}else{var _savedGuideData$Guide29,_savedGuideData$Guide30;// Fallback to default values for tour hotspots without metadata\nhotspotPropData={XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};hotspotData=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide29=savedGuideData.GuideStep)===null||_savedGuideData$Guide29===void 0?void 0:(_savedGuideData$Guide30=_savedGuideData$Guide29[currentStep-1])===null||_savedGuideData$Guide30===void 0?void 0:_savedGuideData$Guide30.Hotspot)||{};}const xOffset=parseFloat(((_hotspotPropData=hotspotPropData)===null||_hotspotPropData===void 0?void 0:_hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat(((_hotspotPropData2=hotspotPropData)===null||_hotspotPropData2===void 0?void 0:_hotspotPropData2.YPosition)||\"4\");const currentHotspotSize=parseFloat(((_hotspotPropData3=hotspotPropData)===null||_hotspotPropData3===void 0?void 0:_hotspotPropData3.Size)||\"30\");// Update hotspot size state\nsetHotspotSize(currentHotspotSize);let left,top;if(element){const rect=element.getBoundingClientRect();left=rect.x+xOffset;top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));// Calculate popup position below the hotspot\nconst popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}// Check if hotspot already exists, preserve it to maintain _pulseStopped state\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){hotspot=existingHotspot;// Don't reset _pulseStopped if it already exists\n}else{// Create new hotspot only if it doesn't exist\nhotspot=document.createElement(\"div\");hotspot.id=\"hotspotBlink\";// Fixed ID for easier reference\nhotspot._pulseStopped=false;// Set only on creation\ndocument.body.appendChild(hotspot);}hotspot.style.cursor=\"pointer\";hotspot.style.pointerEvents=\"auto\";// Ensure it can receive mouse events\n// Make sure the hotspot is visible and clickable\nhotspot.style.zIndex=\"9999\";// If ShowByDefault is true, set openTooltip to true immediately\nif((_hotspotPropData4=hotspotPropData)!==null&&_hotspotPropData4!==void 0&&_hotspotPropData4.ShowByDefault){setOpenTooltip(true);}// Set styles first\napplyHotspotStyles(hotspot,hotspotPropData,hotspotData,left,top);// Set initial tooltip visibility based on ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{//setOpenTooltip(false);\n}// We don't need to add event listeners here as they're already added in applyHotspotStyles\n}}catch(error){console.error(\"Error in fetchGuideDetails:\",error);}};fetchGuideDetails();return()=>{const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.onclick=null;existingHotspot.onmouseover=null;existingHotspot.onmouseout=null;}};},[savedGuideData,toolTipGuideMetaData,isHotspotPopupOpen,showHotspotenduser,selectedTemplateTour,currentStep// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n]);const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide31=savedGuideData.GuideStep)===null||_savedGuideData$Guide31===void 0?void 0:(_savedGuideData$Guide32=_savedGuideData$Guide31[0])===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32.Tooltip)===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide34,_savedGuideData$Guide35,_savedGuideData$Guide36;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:(_savedGuideData$Guide35=_savedGuideData$Guide34[0])===null||_savedGuideData$Guide35===void 0?void 0:(_savedGuideData$Guide36=_savedGuideData$Guide35.Tooltip)===null||_savedGuideData$Guide36===void 0?void 0:_savedGuideData$Guide36.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",position:\"inherit !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:index===currentStep-1?ProgressColor:\"#e0e0e0\",// Active color and inactive color\nborderRadius:\"100px\"}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",\"& .MuiLinearProgress-bar\":{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsxs(_Fragment,{children:[targetElement&&/*#__PURE__*/_jsx(\"div\",{children:openTooltip&&/*#__PURE__*/_jsxs(Popover,{open:Boolean(popupPosition)||Boolean(anchorEl),anchorEl:anchorEl,onClose:()=>{// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on Popover close events\n},anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,anchorReference:\"anchorPosition\",anchorPosition:popupPosition?{top:popupPosition.top+(parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\"))),left:popupPosition.left+parseFloat(tooltipXaxis||\"0\")}:undefined,sx:{// \"& .MuiBackdrop-root\": {\n//     position: 'relative !important', // Ensures higher specificity\n// },\n\"pointer-events\":anchorEl?\"auto\":\"auto\",'& .MuiPaper-root:not(.MuiMobileStepper-root)':{zIndex:1000,// borderRadius: \"1px\",\n...canvasStyle,//...getAnchorAndTransformOrigins,\n//top: \"16% !important\",\n// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n...getCanvasPosition((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\"),top:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.top)||0)+(tooltipYaxis&&tooltipYaxis!='undefined'?parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\")):0)}px !important`,left:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.left)||0)+(tooltipXaxis&&tooltipXaxis!='undefined'?parseFloat(tooltipXaxis)||0:0)}px !important`,overflow:\"hidden\",// Add smooth transitions for position changes (enhanced with user preference)\ntransition:'top 0.25s cubic-bezier(0.4, 0, 0.2, 1), left 0.25s cubic-bezier(0.4, 0, 0.2, 1), width 0.25s cubic-bezier(0.4, 0, 0.2, 1), max-width 0.25s cubic-bezier(0.4, 0, 0.2, 1)'}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{onClick:()=>{// Only close if explicitly requested by user clicking the close button\n//setOpenTooltip(false);\n},sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"400px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:\"400px\",overflow:\"hidden auto\"},children:/*#__PURE__*/_jsxs(Box,{style:{// Remove padding here since it's now handled in canvasStyle\nheight:sectionHeight},children:[/*#__PURE__*/_jsxs(Box,{ref:contentRef,display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",sx:{width:\"100%\",padding:\"8px\",boxSizing:\"border-box\"},children:[imageProperties===null||imageProperties===void 0?void 0:imageProperties.map(imageProp=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",//  width: \"500px\",\nheight:`${customImg.SectionHeight||250}px`,background:customImg.BackgroundColor||\"#ffffff\",margin:\"10px 0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`))),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper,_textField$TextProper2;return textField.Text&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview\",// Use a unique key, either Id or index\nsx:{textAlign:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextFormat)||textStyle.textAlign,color:((_textField$TextProper2=textField.TextProperties)===null||_textField$TextProper2===void 0?void 0:_textField$TextProper2.TextColor)||textStyle.color,whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",padding:\"0 5px\",wordWrap:\"break-word\",overflowWrap:\"break-word\",hyphens:\"auto\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);})]}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:\"5px 0\",backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:\"5px 0\",width:\"100%\"},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6,_button$ButtonPropert7;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{margin:\"0 5px 5px 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"15px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:\"4px 8px\",lineHeight:\"normal\",textTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:((_button$ButtonPropert7=button.ButtonProperties)===null||_button$ButtonPropert7===void 0?void 0:_button$ButtonPropert7.ButtonBackgroundColor)||\"#007bff\",// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:button.ButtonName},index);})},containerId);})]})})},`scrollbar-${needsScrolling}`),enableProgress&&totalSteps>1&&selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `})]});};export default HotspotPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HotspotPreview", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "element", "rect", "getBoundingClientRect", "top", "left", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "getEstimatedPopupDimensions", "current", "contentRect", "actualWidth", "Math", "max", "width", "actualHeight", "height", "widthStyling", "getWidthStyling", "constrained<PERSON>idth", "includes", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "replace", "parseInt", "min", "fixedWidthValue", "estimatedWidth", "contentBasedWidth", "length", "totalTextLength", "reduce", "sum", "field", "Text", "avgTextLength", "buttonWidth", "estimatedHeight", "calculateBestPosition", "elementRect", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "spaceTop", "spaceBottom", "bottom", "spaceLeft", "spaceRight", "right", "maxSpace", "calculatePopupPosition", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "popup<PERSON><PERSON><PERSON>", "popupHeight", "VIEWPORT_MARGIN", "TOOLTIP_GAP", "TOOLTIP_GAP_ABOVE", "availableWidth", "availableHeight", "<PERSON><PERSON><PERSON>gin", "hotspotRight", "hotspotBottom", "hotspotCenterX", "hotspotCenterY", "positions", "name", "isPositionValid", "pos", "selectedPosition", "find", "process", "env", "NODE_ENV", "console", "log", "hotspotPosition", "popupDimensions", "viewport", "finalPosition", "scrollY", "scrollX", "undefined", "position", "_guideStep", "style", "backgroundColor", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "acc", "curr", "concat", "Hotspot", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "_match", "p1", "p2", "p3", "isCanvasWidthModified", "_canvasProperties$Wid", "defaultWidth", "currentWidth", "<PERSON><PERSON><PERSON>", "hasCustomWidth", "userWidth", "min<PERSON><PERSON><PERSON>", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "_toolTipGuideMetaData2", "handleResize", "_toolTipGuideMetaData3", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "padding", "Padding", "boxShadow", "border", "sectionHeight", "CustomImage", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "arguments", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "background", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "flexDirection", "flexWrap", "boxSizing", "imageProp", "customImg", "imgIndex", "component", "src", "Url", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "wordWrap", "overflowWrap", "hyphens", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to get estimated popup dimensions based on actual content (enhanced to match TooltipPreview)\r\n\tconst getEstimatedPopupDimensions = () => {\r\n\t\t// Try to get actual dimensions from content if available (similar to TooltipPreview approach)\r\n\t\tif (contentRef.current) {\r\n\t\t\tconst contentRect = contentRef.current.getBoundingClientRect();\r\n\t\t\tconst actualWidth = Math.max(contentRect.width, 50); // Minimum width\r\n\t\t\tconst actualHeight = Math.max(contentRect.height + 100, 150); // Add padding for buttons/progress\r\n\r\n\t\t\t// Apply width constraints similar to TooltipPreview's approach\r\n\t\t\tconst widthStyling = getWidthStyling();\r\n\t\t\tlet constrainedWidth = actualWidth;\r\n\r\n\t\t\t// Check if using auto width (like TooltipPreview's 'auto !important')\r\n\t\t\tif (widthStyling.width.includes('auto')) {\r\n\t\t\t\t// For auto-width, respect maxWidth constraint (like TooltipPreview's maxWidth approach)\r\n\t\t\t\tconst maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\t\tconst maxWidth = parseInt(maxWidthValue) || 300;\r\n\t\t\t\tconstrainedWidth = Math.min(actualWidth, maxWidth);\r\n\t\t\t} else {\r\n\t\t\t\t// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\r\n\t\t\t\tconst fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\t\tconstrainedWidth = parseInt(fixedWidthValue) || actualWidth;\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\twidth: constrainedWidth,\r\n\t\t\t\theight: actualHeight\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Fallback estimation when content ref is not available (enhanced to match TooltipPreview)\r\n\t\tconst widthStyling = getWidthStyling();\r\n\t\tlet estimatedWidth = 300; // Default fallback\r\n\r\n\t\t// Check if using auto width (like TooltipPreview's 'auto !important')\r\n\t\tif (widthStyling.width.includes('auto')) {\r\n\t\t\t// For auto-width, estimate based on content type and respect maxWidth (like TooltipPreview)\r\n\t\t\tconst maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\tconst maxWidth = parseInt(maxWidthValue) || 300;\r\n\r\n\t\t\t// Enhanced content-based width estimation (similar to TooltipPreview's approach)\r\n\t\t\tlet contentBasedWidth = 250; // Base estimate\r\n\r\n\t\t\t// Adjust based on content types present (similar to TooltipPreview's content analysis)\r\n\t\t\tif (textFieldProperties && textFieldProperties.length > 0) {\r\n\t\t\t\t// Estimate text width based on content length (enhanced calculation)\r\n\t\t\t\tconst totalTextLength = textFieldProperties.reduce((sum: number, field: any) =>\r\n\t\t\t\t\tsum + (field.Text ? field.Text.length : 0), 0);\r\n\t\t\t\tconst avgTextLength = totalTextLength / textFieldProperties.length;\r\n\t\t\t\t// Use character-based width estimation similar to TooltipPreview\r\n\t\t\t\tcontentBasedWidth = Math.min(Math.max(avgTextLength * 7.5, 200), maxWidth);\r\n\t\t\t}\r\n\r\n\t\t\tif (imageProperties && imageProperties.length > 0) {\r\n\t\t\t\t// Images typically need more width (similar to TooltipPreview's image handling)\r\n\t\t\t\tcontentBasedWidth = Math.max(contentBasedWidth, 280);\r\n\t\t\t}\r\n\r\n\t\t\tif (customButton && customButton.length > 0) {\r\n\t\t\t\t// Buttons may need additional width (enhanced button width calculation)\r\n\t\t\t\tconst buttonWidth = customButton.length * 85; // Slightly larger estimate for better fit\r\n\t\t\t\tcontentBasedWidth = Math.max(contentBasedWidth, Math.min(buttonWidth + 50, maxWidth));\r\n\t\t\t}\r\n\r\n\t\t\testimatedWidth = Math.min(contentBasedWidth, maxWidth);\r\n\t\t} else {\r\n\t\t\t// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\r\n\t\t\tconst fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\testimatedWidth = parseInt(fixedWidthValue) || 300;\r\n\t\t}\r\n\r\n\t\tconst estimatedHeight = 250; // Standard height for all content types\r\n\r\n\t\treturn {\r\n\t\t\twidth: estimatedWidth,\r\n\t\t\theight: estimatedHeight\r\n\t\t};\r\n\t};\r\n\r\n\t// Function to calculate best position based on available space (similar to TooltipPreview)\r\n\tconst calculateBestPosition = (elementRect: DOMRect): \"top\" | \"left\" | \"right\" | \"bottom\" => {\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\tconst spaceTop = elementRect.top;\r\n\t\tconst spaceBottom = viewportHeight - elementRect.bottom;\r\n\t\tconst spaceLeft = elementRect.left;\r\n\t\tconst spaceRight = viewportWidth - elementRect.right;\r\n\r\n\t\tconst maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\r\n\r\n\t\tif (maxSpace === spaceTop) return \"top\";\r\n\t\tif (maxSpace === spaceBottom) return \"bottom\";\r\n\t\tif (maxSpace === spaceLeft) return \"left\";\r\n\t\treturn \"right\";\r\n\t};\r\n\r\n\t// Function to calculate smart popup position that stays within viewport (enhanced with TooltipPreview logic)\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Get viewport dimensions\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\t// Get estimated popup dimensions with enhanced width calculation\r\n\t\tconst { width: popupWidth, height: popupHeight } = getEstimatedPopupDimensions();\r\n\r\n\t\t// Viewport margin to ensure tooltip doesn't touch edges (similar to TooltipPreview's preventOverflow)\r\n\t\tconst VIEWPORT_MARGIN = 20;\r\n\r\n\t\t// Gap between hotspot beacon and tooltip (similar to TooltipPreview's arrow spacing)\r\n\t\tconst TOOLTIP_GAP = 12;\r\n\r\n\t\t// Smaller gap for tooltips positioned above hotspots (closer to beacon for better visual connection)\r\n\t\tconst TOOLTIP_GAP_ABOVE = 8;\r\n\r\n\t\t// Handle very small viewports - reduce margins if popup is too large\r\n\t\tconst availableWidth = viewportWidth - (VIEWPORT_MARGIN * 2);\r\n\t\tconst availableHeight = viewportHeight - (VIEWPORT_MARGIN * 2);\r\n\t\tconst effectiveMargin = Math.min(VIEWPORT_MARGIN,\r\n\t\t\tMath.max(5, Math.min((availableWidth - popupWidth) / 2, (availableHeight - popupHeight) / 2))\r\n\t\t);\r\n\r\n\t\t// Calculate hotspot beacon bounds (the visual indicator/marker)\r\n\t\tconst hotspotRight = hotspotLeft + hotspotSize;\r\n\t\tconst hotspotBottom = hotspotTop + hotspotSize;\r\n\t\tconst hotspotCenterX = hotspotLeft + (hotspotSize / 2);\r\n\t\tconst hotspotCenterY = hotspotTop + (hotspotSize / 2);\r\n\r\n\t\t// Define possible positions in order of preference (enhanced with center-based positioning)\r\n\t\tconst positions = [\r\n\t\t\t// Primary: bottom-center (positioned below hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-center',\r\n\t\t\t\tleft: hotspotCenterX - (popupWidth / 2),\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Secondary: top-center (positioned above hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'top-center',\r\n\t\t\t\tleft: hotspotCenterX - (popupWidth / 2),\r\n\t\t\t\ttop: hotspotTop - popupHeight - TOOLTIP_GAP_ABOVE\r\n\t\t\t},\r\n\t\t\t// Tertiary: right-center (positioned to the right of hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'right-center',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotCenterY - (popupHeight / 2)\r\n\t\t\t},\r\n\t\t\t// Quaternary: left-center (positioned to the left of hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'left-center',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotCenterY - (popupHeight / 2)\r\n\t\t\t},\r\n\t\t\t// Fallback: bottom-right (original default)\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-right',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Fallback: bottom-left\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-left',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\t// Function to check if a position fits within viewport\r\n\t\tconst isPositionValid = (pos: { left: number; top: number }) => {\r\n\t\t\tconst right = pos.left + popupWidth;\r\n\t\t\tconst bottom = pos.top + popupHeight;\r\n\r\n\t\t\treturn (\r\n\t\t\t\tpos.left >= effectiveMargin &&\r\n\t\t\t\tpos.top >= effectiveMargin &&\r\n\t\t\t\tright <= viewportWidth - effectiveMargin &&\r\n\t\t\t\tbottom <= viewportHeight - effectiveMargin\r\n\t\t\t);\r\n\t\t};\r\n\r\n\t\t// Find the first valid position\r\n\t\tlet selectedPosition = positions.find(pos => isPositionValid(pos));\r\n\r\n\t\t// If no position fits perfectly, use the default and adjust to fit\r\n\t\tif (!selectedPosition) {\r\n\t\t\tselectedPosition = positions[0]; // Default: bottom-right\r\n\r\n\t\t\t// Adjust horizontally if needed\r\n\t\t\tif (selectedPosition.left + popupWidth > viewportWidth - effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = viewportWidth - popupWidth - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.left < effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = effectiveMargin;\r\n\t\t\t}\r\n\r\n\t\t\t// Adjust vertically if needed\r\n\t\t\tif (selectedPosition.top + popupHeight > viewportHeight - effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = viewportHeight - popupHeight - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.top < effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = effectiveMargin;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Debug logging (can be removed in production)\r\n\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\tconsole.log('Smart positioning:', {\r\n\t\t\t\thotspotPosition: { left: hotspotLeft, top: hotspotTop },\r\n\t\t\t\thotspotSize: hotspotSize,\r\n\t\t\t\tpopupDimensions: { width: popupWidth, height: popupHeight },\r\n\t\t\t\tviewport: { width: viewportWidth, height: viewportHeight },\r\n\t\t\t\tselectedPosition: selectedPosition.name,\r\n\t\t\t\tfinalPosition: { left: selectedPosition.left, top: selectedPosition.top }\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\ttop: selectedPosition.top + window.scrollY,\r\n\t\t\tleft: selectedPosition.left + window.scrollX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\r\n\r\n\t// Function to check if canvas width has been modified from default\r\n\tconst isCanvasWidthModified = () => {\r\n\t\tconst defaultWidth = \"300\"; // Default width without \"px\"\r\n\t\tconst currentWidth = canvasProperties?.Width?.replace(\"px\", \"\") || defaultWidth;\r\n\t\treturn currentWidth !== defaultWidth;\r\n\t};\r\n\r\n\t// Function to get width styling based on user preferences (enhanced to match TooltipPreview approach)\r\n\tconst getWidthStyling = () => {\r\n\t\t// Check if user has modified canvas width from default\r\n\t\tconst hasCustomWidth = isCanvasWidthModified();\r\n\r\n\t\tif (hasCustomWidth && canvasProperties?.Width) {\r\n\t\t\t// User-specified width: use exact width with !important, matching TooltipPreview's approach\r\n\t\t\tconst userWidth = canvasProperties.Width.includes('px')\r\n\t\t\t\t? canvasProperties.Width\r\n\t\t\t\t: `${canvasProperties.Width}px`;\r\n\t\t\treturn {\r\n\t\t\t\twidth: `${userWidth} !important`, // Use !important like TooltipPreview\r\n\t\t\t\tmaxWidth: `${userWidth} !important`, // Override the default limit with !important\r\n\t\t\t\tminWidth: 'unset' // Remove any minimum width constraints\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\t// Default behavior: auto-adjust with content-based sizing, exactly like TooltipPreview\r\n\t\t\t// TooltipPreview uses: width: 'auto !important', maxWidth: canvasStyle?.Width || \"300px\"\r\n\t\t\treturn {\r\n\t\t\t\twidth: 'auto !important', // Match TooltipPreview's exact approach\r\n\t\t\t\tmaxWidth: '300px !important', // Default maximum width constraint with !important\r\n\t\t\t\tminWidth: 'unset' // Remove minimum width constraints for small content\r\n\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t// Update popup position when content changes (width is now calculated dynamically)\r\n\tuseEffect(() => {\r\n\t\t// Recalculate popup position when content changes since width affects positioning\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position when hotspot size changes or content dimensions change\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst widthStyling = getWidthStyling();\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\t// Apply padding similar to TooltipPreview approach\r\n\t\tpadding: canvasProperties?.Padding || \"10px\",\r\n\t\t// Apply width styling with enhanced approach matching TooltipPreview\r\n\t\t...widthStyling,\r\n\t\t// Add box shadow similar to TooltipPreview for consistency\r\n\t\tboxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\r\n\t\t// Add border handling similar to TooltipPreview\r\n\t\tborder: canvasProperties?.BorderSize && canvasProperties?.BorderSize !== \"0px\"\r\n\t\t\t? `${canvasProperties?.BorderSize} solid ${canvasProperties?.BorderColor || \"transparent\"}`\r\n\t\t\t: \"none\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left + parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t// Add smooth transitions for position changes (enhanced with user preference)\r\n\t\t\t\t\t\t\t\t\ttransition: 'top 0.25s cubic-bezier(0.4, 0, 0.2, 1), left 0.25s cubic-bezier(0.4, 0, 0.2, 1), width 0.25s cubic-bezier(0.4, 0, 0.2, 1), max-width 0.25s cubic-bezier(0.4, 0, 0.2, 1)',\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"400px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: \"hidden auto\"\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\t// Remove padding here since it's now handled in canvasStyle\r\n\t\t\t\t\t\t\t\t\theight: sectionHeight\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxSizing: \"border-box\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflowWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thyphens: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t\t\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,cAAc,CAAEC,aAAa,CAAEC,OAAO,CAAiBC,UAAU,KAAQ,eAAe,CAE1H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE;AACA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA6GrD,KAAM,CAAAC,cAAoC,CAAGC,IAAA,EA8BvC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IA9BwC,CAC1CC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,kBAAkB,CAClBC,kBAAkB,CAClBC,kBAAkB,CACnBC,kBAEH,CAAC,CAAAvC,IAAA,CACA,KAAM,CACLwC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,aACD,CAAC,CAAG9D,cAAc,CAAE+D,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG3E,QAAQ,CAAqB,IAAI,CAAC,CAC5E;AACA;AACA,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAuC,IAAI,CAAC,CAC9F,KAAM,CAAC8E,WAAW,CAAEC,cAAc,CAAC,CAAG/E,QAAQ,CAAS,EAAE,CAAC,CAAE;AAC5D,KAAM,CAAAgF,UAAU,CAAG/E,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAAgF,kBAAkB,CAAGhF,MAAM,CAAiB,IAAI,CAAC,CACvD,GAAI,CAAAiF,OAAY,CAChB,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAChE,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,CAAEE,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CACnC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CAChC,MAAO,CAAAF,IAAI,CACZ,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAC/B,MAAO,CAAAH,IAAI,CAACG,aAAa,CAAE;AAC5B,CAAC,IAAM,CACN,MAAO,KAAI,CACZ,CACD,CAAC,CACD,GAAI,CAAAT,KAAU,CACd,GAAI/B,cAAc,CAAE+B,KAAK,CAAG/B,cAAc,SAAdA,cAAc,kBAAAjC,qBAAA,CAAdiC,cAAc,CAAEyC,SAAS,UAAA1E,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,iBAA9BA,sBAAA,CAAgC0E,WAAW,CACvE,KAAM,CAAAC,kBAAkB,CAAIZ,KAAyB,EAAK,CACzD,KAAM,CAAAa,OAAO,CAAGd,iBAAiB,CAACC,KAAK,EAAI,EAAE,CAAC,CAC9C,GAAIa,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,MAAO,CACNC,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAE;AACfC,IAAI,CAAEH,IAAI,CAACG,IAAM;AAClB,CAAC,CACF,CACA,MAAO,KAAI,CACZ,CAAC,CACC;AACA,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAwG,YAAY,CAAGvG,MAAM,CAAM,IAAI,CAAC,CACxC;AACA,KAAM,CAAAwG,2BAA2B,CAAGA,CAAA,GAAM,CACzC;AACA,GAAIzB,UAAU,CAAC0B,OAAO,CAAE,CACvB,KAAM,CAAAC,WAAW,CAAG3B,UAAU,CAAC0B,OAAO,CAACP,qBAAqB,CAAC,CAAC,CAC9D,KAAM,CAAAS,WAAW,CAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,CAACI,KAAK,CAAE,EAAE,CAAC,CAAE;AACrD,KAAM,CAAAC,YAAY,CAAGH,IAAI,CAACC,GAAG,CAACH,WAAW,CAACM,MAAM,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AAE9D;AACA,KAAM,CAAAC,YAAY,CAAGC,eAAe,CAAC,CAAC,CACtC,GAAI,CAAAC,gBAAgB,CAAGR,WAAW,CAElC;AACA,GAAIM,YAAY,CAACH,KAAK,CAACM,QAAQ,CAAC,MAAM,CAAC,CAAE,CACxC;AACA,KAAM,CAAAC,aAAa,CAAGJ,YAAY,CAACK,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CAAE;AACnE,KAAM,CAAAD,QAAQ,CAAGE,QAAQ,CAACH,aAAa,CAAC,EAAI,GAAG,CAC/CF,gBAAgB,CAAGP,IAAI,CAACa,GAAG,CAACd,WAAW,CAAEW,QAAQ,CAAC,CACnD,CAAC,IAAM,CACN;AACA,KAAM,CAAAI,eAAe,CAAGT,YAAY,CAACH,KAAK,CAACS,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CAAE;AAClEJ,gBAAgB,CAAGK,QAAQ,CAACE,eAAe,CAAC,EAAIf,WAAW,CAC5D,CAEA,MAAO,CACNG,KAAK,CAAEK,gBAAgB,CACvBH,MAAM,CAAED,YACT,CAAC,CACF,CAEA;AACA,KAAM,CAAAE,YAAY,CAAGC,eAAe,CAAC,CAAC,CACtC,GAAI,CAAAS,cAAc,CAAG,GAAG,CAAE;AAE1B;AACA,GAAIV,YAAY,CAACH,KAAK,CAACM,QAAQ,CAAC,MAAM,CAAC,CAAE,CACxC;AACA,KAAM,CAAAC,aAAa,CAAGJ,YAAY,CAACK,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CAAE;AACnE,KAAM,CAAAD,QAAQ,CAAGE,QAAQ,CAACH,aAAa,CAAC,EAAI,GAAG,CAE/C;AACA,GAAI,CAAAO,iBAAiB,CAAG,GAAG,CAAE;AAE7B;AACA,GAAIjF,mBAAmB,EAAIA,mBAAmB,CAACkF,MAAM,CAAG,CAAC,CAAE,CAC1D;AACA,KAAM,CAAAC,eAAe,CAAGnF,mBAAmB,CAACoF,MAAM,CAAC,CAACC,GAAW,CAAEC,KAAU,GAC1ED,GAAG,EAAIC,KAAK,CAACC,IAAI,CAAGD,KAAK,CAACC,IAAI,CAACL,MAAM,CAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAC/C,KAAM,CAAAM,aAAa,CAAGL,eAAe,CAAGnF,mBAAmB,CAACkF,MAAM,CAClE;AACAD,iBAAiB,CAAGhB,IAAI,CAACa,GAAG,CAACb,IAAI,CAACC,GAAG,CAACsB,aAAa,CAAG,GAAG,CAAE,GAAG,CAAC,CAAEb,QAAQ,CAAC,CAC3E,CAEA,GAAI1E,eAAe,EAAIA,eAAe,CAACiF,MAAM,CAAG,CAAC,CAAE,CAClD;AACAD,iBAAiB,CAAGhB,IAAI,CAACC,GAAG,CAACe,iBAAiB,CAAE,GAAG,CAAC,CACrD,CAEA,GAAI/E,YAAY,EAAIA,YAAY,CAACgF,MAAM,CAAG,CAAC,CAAE,CAC5C;AACA,KAAM,CAAAO,WAAW,CAAGvF,YAAY,CAACgF,MAAM,CAAG,EAAE,CAAE;AAC9CD,iBAAiB,CAAGhB,IAAI,CAACC,GAAG,CAACe,iBAAiB,CAAEhB,IAAI,CAACa,GAAG,CAACW,WAAW,CAAG,EAAE,CAAEd,QAAQ,CAAC,CAAC,CACtF,CAEAK,cAAc,CAAGf,IAAI,CAACa,GAAG,CAACG,iBAAiB,CAAEN,QAAQ,CAAC,CACvD,CAAC,IAAM,CACN;AACA,KAAM,CAAAI,eAAe,CAAGT,YAAY,CAACH,KAAK,CAACS,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAC,CAAE;AAClEI,cAAc,CAAGH,QAAQ,CAACE,eAAe,CAAC,EAAI,GAAG,CAClD,CAEA,KAAM,CAAAW,eAAe,CAAG,GAAG,CAAE;AAE7B,MAAO,CACNvB,KAAK,CAAEa,cAAc,CACrBX,MAAM,CAAEqB,eACT,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,qBAAqB,CAAIC,WAAoB,EAA0C,CAC5F,KAAM,CAAAC,aAAa,CAAGC,MAAM,CAACC,UAAU,CACvC,KAAM,CAAAC,cAAc,CAAGF,MAAM,CAACG,WAAW,CAEzC,KAAM,CAAAC,QAAQ,CAAGN,WAAW,CAACpC,GAAG,CAChC,KAAM,CAAA2C,WAAW,CAAGH,cAAc,CAAGJ,WAAW,CAACQ,MAAM,CACvD,KAAM,CAAAC,SAAS,CAAGT,WAAW,CAACnC,IAAI,CAClC,KAAM,CAAA6C,UAAU,CAAGT,aAAa,CAAGD,WAAW,CAACW,KAAK,CAEpD,KAAM,CAAAC,QAAQ,CAAGvC,IAAI,CAACC,GAAG,CAACgC,QAAQ,CAAEC,WAAW,CAAEE,SAAS,CAAEC,UAAU,CAAC,CAEvE,GAAIE,QAAQ,GAAKN,QAAQ,CAAE,MAAO,KAAK,CACvC,GAAIM,QAAQ,GAAKL,WAAW,CAAE,MAAO,QAAQ,CAC7C,GAAIK,QAAQ,GAAKH,SAAS,CAAE,MAAO,MAAM,CACzC,MAAO,OAAO,CACf,CAAC,CAED;AACA,KAAM,CAAAI,sBAAsB,CAAGA,CAACb,WAAoB,CAAE1D,WAAmB,CAAEwE,OAAe,CAAEC,OAAe,GAAK,CAC/G,KAAM,CAAAC,WAAW,CAAGhB,WAAW,CAACiB,CAAC,CAAGH,OAAO,CAC3C,KAAM,CAAAI,UAAU,CAAGlB,WAAW,CAACmB,CAAC,CAAGJ,OAAO,CAE1C;AACA,KAAM,CAAAd,aAAa,CAAGC,MAAM,CAACC,UAAU,CACvC,KAAM,CAAAC,cAAc,CAAGF,MAAM,CAACG,WAAW,CAEzC;AACA,KAAM,CAAE9B,KAAK,CAAE6C,UAAU,CAAE3C,MAAM,CAAE4C,WAAY,CAAC,CAAGpD,2BAA2B,CAAC,CAAC,CAEhF;AACA,KAAM,CAAAqD,eAAe,CAAG,EAAE,CAE1B;AACA,KAAM,CAAAC,WAAW,CAAG,EAAE,CAEtB;AACA,KAAM,CAAAC,iBAAiB,CAAG,CAAC,CAE3B;AACA,KAAM,CAAAC,cAAc,CAAGxB,aAAa,CAAIqB,eAAe,CAAG,CAAE,CAC5D,KAAM,CAAAI,eAAe,CAAGtB,cAAc,CAAIkB,eAAe,CAAG,CAAE,CAC9D,KAAM,CAAAK,eAAe,CAAGtD,IAAI,CAACa,GAAG,CAACoC,eAAe,CAC/CjD,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACa,GAAG,CAAC,CAACuC,cAAc,CAAGL,UAAU,EAAI,CAAC,CAAE,CAACM,eAAe,CAAGL,WAAW,EAAI,CAAC,CAAC,CAC7F,CAAC,CAED;AACA,KAAM,CAAAO,YAAY,CAAGZ,WAAW,CAAG1E,WAAW,CAC9C,KAAM,CAAAuF,aAAa,CAAGX,UAAU,CAAG5E,WAAW,CAC9C,KAAM,CAAAwF,cAAc,CAAGd,WAAW,CAAI1E,WAAW,CAAG,CAAE,CACtD,KAAM,CAAAyF,cAAc,CAAGb,UAAU,CAAI5E,WAAW,CAAG,CAAE,CAErD;AACA,KAAM,CAAA0F,SAAS,CAAG,CACjB;AACA,CACCC,IAAI,CAAE,eAAe,CACrBpE,IAAI,CAAEiE,cAAc,CAAIV,UAAU,CAAG,CAAE,CACvCxD,GAAG,CAAEiE,aAAa,CAAGN,WACtB,CAAC,CACD;AACA,CACCU,IAAI,CAAE,YAAY,CAClBpE,IAAI,CAAEiE,cAAc,CAAIV,UAAU,CAAG,CAAE,CACvCxD,GAAG,CAAEsD,UAAU,CAAGG,WAAW,CAAGG,iBACjC,CAAC,CACD;AACA,CACCS,IAAI,CAAE,cAAc,CACpBpE,IAAI,CAAE+D,YAAY,CAAGL,WAAW,CAChC3D,GAAG,CAAEmE,cAAc,CAAIV,WAAW,CAAG,CACtC,CAAC,CACD;AACA,CACCY,IAAI,CAAE,aAAa,CACnBpE,IAAI,CAAEmD,WAAW,CAAGI,UAAU,CAAGG,WAAW,CAC5C3D,GAAG,CAAEmE,cAAc,CAAIV,WAAW,CAAG,CACtC,CAAC,CACD;AACA,CACCY,IAAI,CAAE,cAAc,CACpBpE,IAAI,CAAE+D,YAAY,CAAGL,WAAW,CAChC3D,GAAG,CAAEiE,aAAa,CAAGN,WACtB,CAAC,CACD;AACA,CACCU,IAAI,CAAE,aAAa,CACnBpE,IAAI,CAAEmD,WAAW,CAAGI,UAAU,CAAGG,WAAW,CAC5C3D,GAAG,CAAEiE,aAAa,CAAGN,WACtB,CAAC,CACD,CAED;AACA,KAAM,CAAAW,eAAe,CAAIC,GAAkC,EAAK,CAC/D,KAAM,CAAAxB,KAAK,CAAGwB,GAAG,CAACtE,IAAI,CAAGuD,UAAU,CACnC,KAAM,CAAAZ,MAAM,CAAG2B,GAAG,CAACvE,GAAG,CAAGyD,WAAW,CAEpC,MACC,CAAAc,GAAG,CAACtE,IAAI,EAAI8D,eAAe,EAC3BQ,GAAG,CAACvE,GAAG,EAAI+D,eAAe,EAC1BhB,KAAK,EAAIV,aAAa,CAAG0B,eAAe,EACxCnB,MAAM,EAAIJ,cAAc,CAAGuB,eAAe,CAE5C,CAAC,CAED;AACA,GAAI,CAAAS,gBAAgB,CAAGJ,SAAS,CAACK,IAAI,CAACF,GAAG,EAAID,eAAe,CAACC,GAAG,CAAC,CAAC,CAElE;AACA,GAAI,CAACC,gBAAgB,CAAE,CACtBA,gBAAgB,CAAGJ,SAAS,CAAC,CAAC,CAAC,CAAE;AAEjC;AACA,GAAII,gBAAgB,CAACvE,IAAI,CAAGuD,UAAU,CAAGnB,aAAa,CAAG0B,eAAe,CAAE,CACzES,gBAAgB,CAACvE,IAAI,CAAGoC,aAAa,CAAGmB,UAAU,CAAGO,eAAe,CACrE,CACA,GAAIS,gBAAgB,CAACvE,IAAI,CAAG8D,eAAe,CAAE,CAC5CS,gBAAgB,CAACvE,IAAI,CAAG8D,eAAe,CACxC,CAEA;AACA,GAAIS,gBAAgB,CAACxE,GAAG,CAAGyD,WAAW,CAAGjB,cAAc,CAAGuB,eAAe,CAAE,CAC1ES,gBAAgB,CAACxE,GAAG,CAAGwC,cAAc,CAAGiB,WAAW,CAAGM,eAAe,CACtE,CACA,GAAIS,gBAAgB,CAACxE,GAAG,CAAG+D,eAAe,CAAE,CAC3CS,gBAAgB,CAACxE,GAAG,CAAG+D,eAAe,CACvC,CACD,CAEA;AACA,GAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC3CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE,CACjCC,eAAe,CAAE,CAAE9E,IAAI,CAAEmD,WAAW,CAAEpD,GAAG,CAAEsD,UAAW,CAAC,CACvD5E,WAAW,CAAEA,WAAW,CACxBsG,eAAe,CAAE,CAAErE,KAAK,CAAE6C,UAAU,CAAE3C,MAAM,CAAE4C,WAAY,CAAC,CAC3DwB,QAAQ,CAAE,CAAEtE,KAAK,CAAE0B,aAAa,CAAExB,MAAM,CAAE2B,cAAe,CAAC,CAC1DgC,gBAAgB,CAAEA,gBAAgB,CAACH,IAAI,CACvCa,aAAa,CAAE,CAAEjF,IAAI,CAAEuE,gBAAgB,CAACvE,IAAI,CAAED,GAAG,CAAEwE,gBAAgB,CAACxE,GAAI,CACzE,CAAC,CAAC,CACH,CAEA,MAAO,CACNA,GAAG,CAAEwE,gBAAgB,CAACxE,GAAG,CAAGsC,MAAM,CAAC6C,OAAO,CAC1ClF,IAAI,CAAEuE,gBAAgB,CAACvE,IAAI,CAAGqC,MAAM,CAAC8C,OACtC,CAAC,CACF,CAAC,CACDzL,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBuB,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAGsC,MAAM,CAAC6C,OAAO,CAAE;AAChClF,IAAI,CAAEH,IAAI,CAACG,IAAI,CAAGqC,MAAM,CAAC8C,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACpG,KAAK,CAAC,CAAC,CACXrF,SAAS,CAAC,IAAM,CACf,GAAI,MAAO,CAAA2I,MAAM,GAAK+C,SAAS,CAAE,CAChC,KAAM,CAAAC,QAAQ,CAAG1F,kBAAkB,CAACZ,KAAK,EAAI,EAAE,CAAC,CAChD,GAAIsG,QAAQ,CAAE,CACb7G,gBAAgB,CAAC6G,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAACtG,KAAK,CAAC,CAAC,CACXrF,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC;AACA,GAAIa,OAAO,CAAE,CACb,CACD,CAAC,CAAE,CAAC5C,cAAc,CAAC,CAAC,CAEpBtD,SAAS,CAAC,IAAM,KAAA4L,UAAA,CACf,KAAM,CAAA1F,OAAO,CAAGd,iBAAiB,CAACnD,SAAS,SAATA,SAAS,kBAAA2J,UAAA,CAAT3J,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAmJ,UAAA,iBAA5BA,UAAA,CAA8B5F,WAAW,CAAC,CAC5EpB,gBAAgB,CAACsB,OAAO,CAAC,CACzB,GAAIA,OAAO,CAAE,CACZA,OAAO,CAAC2F,KAAK,CAACC,eAAe,CAAG,gBAAgB,CAEhD;AACA,KAAM,CAAA3F,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBuB,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAGsC,MAAM,CAAC6C,OAAO,CAC9BlF,IAAI,CAAEH,IAAI,CAACG,IAAI,CAAGqC,MAAM,CAAC8C,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACxJ,SAAS,CAAEQ,WAAW,CAAC,CAAC,CAE5B;AACA;AACA,KAAM,EAAGsJ,eAAe,CAAC,CAAG9L,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAA+L,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAInI,gBAAgB,GAAK,MAAM,CAAE,CAChC,GAAIpB,WAAW,CAAGC,UAAU,CAAE,CAC7BkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BF,UAAU,CAAC,CAAC,CACZ0J,eAAe,CAACxJ,WAAW,CAAGC,UAAU,CAAC,CAC1C,CACD,CAAC,IAAM,CACNkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/B,KAAM,CAAAyJ,eAAe,CAAG3G,QAAQ,CAAC4G,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACL,KAAK,CAACO,OAAO,CAAG,MAAM,CACtCF,eAAe,CAACG,MAAM,CAAC,CAAC,CACzB,CACD,CACD,CAAC,CAED,KAAM,CAAAJ,eAAe,CAAIK,qBAA8B,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC3D,MAAO,CAAAV,qBAAqB,cAC3BxL,IAAA,CAACK,cAAc,EACduC,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCH,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCzB,QAAQ,CAAEA,QAAS,CACnBsB,cAAc,CAAEA,cAAe,CAC/BrB,SAAS,CAAEA,SAAU,CACrBI,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAE2K,cAAe,CAC3B1K,UAAU,CAAEyJ,cAAe,CAC3B9J,KAAK,CAAEA,KAAM,CACbC,IAAI,CAAEA,IAAK,CACXC,QAAQ,CAAEA,QAAS,CACnBK,WAAW,CAAEA,WAAW,CAAG,CAAE,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,eAAe,CAAEA,eAAgB,CACjCC,QAAQ,CAAEA,QAAS,CACnBC,mBAAmB,CAAES,cAAc,SAAdA,cAAc,kBAAAiJ,sBAAA,CAAdjJ,cAAc,CAAEyC,SAAS,UAAAwG,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B9J,WAAW,CAAC,UAAA+J,sBAAA,iBAAxCA,sBAAA,CAA0CU,mBAAoB,CACnFpK,eAAe,CAAEQ,cAAc,SAAdA,cAAc,kBAAAmJ,sBAAA,CAAdnJ,cAAc,CAAEyC,SAAS,UAAA0G,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BhK,WAAW,CAAC,UAAAiK,sBAAA,iBAAxCA,sBAAA,CAA0CS,eAAgB,CAC3EpK,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAAqJ,sBAAA,CAAdrJ,cAAc,CAAEyC,SAAS,UAAA4G,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BlK,WAAW,CAAC,UAAAmK,sBAAA,kBAAAC,sBAAA,CAAxCD,sBAAA,CAA0CQ,aAAa,UAAAP,sBAAA,kBAAAC,uBAAA,CAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,EACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CAC3C,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAAZ,uBAAA,iBALDA,uBAAA,CAKG7E,MAAM,CAAC,CAAC0F,GAAmB,CAAEC,IAAS,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACvE,CACD5K,eAAe,CAAEA,eAAgB,CACjCC,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBG,YAAY,CAAEA,YAAa,CAC3BE,iBAAiB,CAAE,CAAAD,cAAc,SAAdA,cAAc,kBAAAyJ,uBAAA,CAAdzJ,cAAc,CAAEyC,SAAS,UAAAgH,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BtK,WAAW,CAAG,CAAC,CAAC,UAAAuK,uBAAA,iBAA5CA,uBAAA,CAA8Cc,OAAO,GAAI,CAAC,CAAE,CAC/E,CAAC,CACC,IAAI,CACT,CAAC,CAED,KAAM,CAAAb,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAIxK,WAAW,CAAG,CAAC,CAAE,CACpBmB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BH,UAAU,CAAC,CAAC,CACb,CACD,CAAC,CACDtC,SAAS,CAAC,IAAM,CACf,GAAIqD,YAAY,CAAE,CACjB0I,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACD,CAAC,CAAE,CAAC1I,YAAY,CAAC,CAAC,CAClB;AACA,KAAM,CAAA0K,4BAA4B,CACjCpC,QAAgB,EACqD,CACrE,OAAQA,QAAQ,EACf,IAAK,UAAU,CACd,MAAO,CACNqC,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CACrDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,WAAW,CACf,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACtDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CACzD,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,YAAY,CAChB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACvDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,QACC,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAEF,YAAY,CAAEG,eAAgB,CAAC,CAAGJ,4BAA4B,CAAC,CAAA9K,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmL,QAAQ,GAAI,eAAe,CAAC,CAErH,KAAM,CAAAC,SAAS,CAAG,CACjBC,UAAU,CAAEzL,mBAAmB,SAAnBA,mBAAmB,YAAAtB,qBAAA,CAAnBsB,mBAAmB,CAAE0L,cAAc,UAAAhN,qBAAA,WAAnCA,qBAAA,CAAqCiN,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAE5L,mBAAmB,SAAnBA,mBAAmB,YAAArB,sBAAA,CAAnBqB,mBAAmB,CAAE0L,cAAc,UAAA/M,sBAAA,WAAnCA,sBAAA,CAAqCkN,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAA9L,mBAAmB,SAAnBA,mBAAmB,kBAAApB,sBAAA,CAAnBoB,mBAAmB,CAAE0L,cAAc,UAAA9M,sBAAA,iBAAnCA,sBAAA,CAAqCmN,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAAhM,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEiM,SAAS,GAAI,MAC9C,CAAC,CAED;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC9C;AACA,MAAO,CACNC,MAAM,CAAED,OAAO,CAACvH,OAAO,CAAC,qCAAqC,CAAE,CAACyH,MAAM,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACtF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CACF,CAAC,CACF,CAAC,CAID;AACA,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACnC,KAAM,CAAAC,YAAY,CAAG,KAAK,CAAE;AAC5B,KAAM,CAAAC,YAAY,CAAG,CAAAxM,gBAAgB,SAAhBA,gBAAgB,kBAAAsM,qBAAA,CAAhBtM,gBAAgB,CAAEyM,KAAK,UAAAH,qBAAA,iBAAvBA,qBAAA,CAAyB9H,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,GAAI+H,YAAY,CAC/E,MAAO,CAAAC,YAAY,GAAKD,YAAY,CACrC,CAAC,CAED;AACA,KAAM,CAAApI,eAAe,CAAGA,CAAA,GAAM,CAC7B;AACA,KAAM,CAAAuI,cAAc,CAAGL,qBAAqB,CAAC,CAAC,CAE9C,GAAIK,cAAc,EAAI1M,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEyM,KAAK,CAAE,CAC9C;AACA,KAAM,CAAAE,SAAS,CAAG3M,gBAAgB,CAACyM,KAAK,CAACpI,QAAQ,CAAC,IAAI,CAAC,CACpDrE,gBAAgB,CAACyM,KAAK,CACtB,GAAGzM,gBAAgB,CAACyM,KAAK,IAAI,CAChC,MAAO,CACN1I,KAAK,CAAE,GAAG4I,SAAS,aAAa,CAAE;AAClCpI,QAAQ,CAAE,GAAGoI,SAAS,aAAa,CAAE;AACrCC,QAAQ,CAAE,OAAQ;AACnB,CAAC,CACF,CAAC,IAAM,CACN;AACA;AACA,MAAO,CACN7I,KAAK,CAAE,iBAAiB,CAAE;AAC1BQ,QAAQ,CAAE,kBAAkB,CAAE;AAC9BqI,QAAQ,CAAE,OAAQ;AACnB,CAAC,CACF,CACD,CAAC,CAED;AACA7P,SAAS,CAAC,IAAM,CACf;AACA,GAAIqF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAA4J,qBAAA,CACZ,KAAM,CAAA3J,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA2J,eAAe,EAAAD,qBAAA,CAAGhM,oBAAoB,CAAC,CAAC,CAAC,UAAAgM,qBAAA,iBAAvBA,qBAAA,CAAyBE,QAAQ,CACzD,KAAM,CAAAzG,OAAO,CAAG0G,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA1G,OAAO,CAAGyG,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAG9G,sBAAsB,CAACnD,IAAI,CAAEpB,WAAW,CAAEwE,OAAO,CAAEC,OAAO,CAAC,CAC5E1E,gBAAgB,CAACsL,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAACvN,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAEN,WAAW,CAAE4C,KAAK,CAAEN,WAAW,CAAEjB,oBAAoB,CAAC,CAAC,CAE/G;AACA9D,SAAS,CAAC,IAAM,CACf,GAAIqF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAAmK,sBAAA,CACZ,KAAM,CAAAlK,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA2J,eAAe,EAAAM,sBAAA,CAAGvM,oBAAoB,CAAC,CAAC,CAAC,UAAAuM,sBAAA,iBAAvBA,sBAAA,CAAyBL,QAAQ,CACzD,KAAM,CAAAzG,OAAO,CAAG0G,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA1G,OAAO,CAAGyG,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAG9G,sBAAsB,CAACnD,IAAI,CAAEpB,WAAW,CAAEwE,OAAO,CAAEC,OAAO,CAAC,CAC5E1E,gBAAgB,CAACsL,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAACrL,WAAW,CAAEM,KAAK,CAAEvB,oBAAoB,CAAEjB,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAC,CAAC,CAElG;AACA/C,SAAS,CAAC,IAAM,CACf,KAAM,CAAAsQ,YAAY,CAAGA,CAAA,GAAM,CAC1B,GAAIjL,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAAqK,sBAAA,CACZ,KAAM,CAAApK,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA2J,eAAe,EAAAQ,sBAAA,CAAGzM,oBAAoB,CAAC,CAAC,CAAC,UAAAyM,sBAAA,iBAAvBA,sBAAA,CAAyBP,QAAQ,CACzD,KAAM,CAAAzG,OAAO,CAAG0G,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA1G,OAAO,CAAGyG,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAG9G,sBAAsB,CAACnD,IAAI,CAAEpB,WAAW,CAAEwE,OAAO,CAAEC,OAAO,CAAC,CAC5E1E,gBAAgB,CAACsL,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAEDzH,MAAM,CAAC6H,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAM3H,MAAM,CAAC8H,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAChE,CAAC,CAAE,CAACjL,KAAK,CAAEN,WAAW,CAAEjB,oBAAoB,CAAC,CAAC,CAE9C,KAAM,CAAA4M,cAAc,CAAG3N,YAAY,CAACkF,MAAM,CAAC,CAAC0F,GAAQ,CAAEH,MAAW,GAAK,CACrE,KAAM,CAAAmD,WAAW,CAAGnD,MAAM,CAACC,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACE,GAAG,CAACgD,WAAW,CAAC,CAAE,CACtBhD,GAAG,CAACgD,WAAW,CAAC,CAAG,EAAE,CACtB,CACAhD,GAAG,CAACgD,WAAW,CAAC,CAACC,IAAI,CAACpD,MAAM,CAAC,CAC7B,MAAO,CAAAG,GAAG,CACX,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAxG,YAAY,CAAGC,eAAe,CAAC,CAAC,CACtC,KAAM,CAAAyJ,WAAW,CAAG,CACnBlF,QAAQ,CAAE,CAAA1I,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmL,QAAQ,GAAI,eAAe,CACvD0C,YAAY,CAAE,CAAA7N,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE8N,MAAM,GAAI,KAAK,CAC/CC,WAAW,CAAE,CAAA/N,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgO,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAAjO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkO,WAAW,GAAI,OAAO,CACrDC,WAAW,CAAE,OAAO,CACpBtF,eAAe,CAAE,CAAA7I,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEoO,eAAe,GAAI,OAAO,CAC7D;AACAC,OAAO,CAAE,CAAArO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEsO,OAAO,GAAI,MAAM,CAC5C;AACA,GAAGpK,YAAY,CACf;AACAqK,SAAS,CAAE,iCAAiC,CAC5C;AACAC,MAAM,CAAExO,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEgO,UAAU,EAAI,CAAAhO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgO,UAAU,IAAK,KAAK,CAC3E,GAAGhO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgO,UAAU,UAAU,CAAAhO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkO,WAAW,GAAI,aAAa,EAAE,CACzF,MACJ,CAAC,CACD,KAAM,CAAAO,aAAa,CAAG,EAAAhQ,gBAAA,CAAAoB,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAf,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkCiQ,WAAW,UAAAhQ,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDc,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAAhEA,sBAAA,CAAkEgQ,aAAa,GAAI,MAAM,CAC/G,KAAM,CAAAC,kBAAkB,CAAIC,MAAW,EAAK,CAC3C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,CAC5F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAvJ,MAAM,CAACwJ,QAAQ,CAACC,IAAI,CAAGJ,SAAS,CACjC,CAAC,IAAM,CACN;AACArJ,MAAM,CAAC0J,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GACCF,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACI,WAAW,EAAI,UAAU,EAChCJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAC/B,CACDjF,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN6E,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACI,WAAW,EAAI,MAAM,EAC5BJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAC3B,CACDlG,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN8F,MAAM,CAACC,MAAM,EAAI,SAAS,EAC1BD,MAAM,CAACI,WAAW,EAAI,SAAS,CAC9B,KAAAI,uBAAA,CAAAC,uBAAA,CACD;AACA3O,cAAc,CAAC,CAAC,CAAC,CACjB;AACA,GAAIN,cAAc,SAAdA,cAAc,YAAAgP,uBAAA,CAAdhP,cAAc,CAAEyC,SAAS,UAAAuM,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgCvM,WAAW,CAAE,CAChD,KAAM,CAAAwM,gBAAgB,CAAGpN,iBAAiB,CAAC9B,cAAc,CAACyC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACnF,GAAIwM,gBAAgB,CAAE,CACrBA,gBAAgB,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACxD,CACD,CACD,CACD,CACA3G,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACD/L,SAAS,CAAC,IAAM,KAAA2S,WAAA,CAAAC,mBAAA,CACf,GAAI3Q,SAAS,SAATA,SAAS,YAAA0Q,WAAA,CAAT1Q,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAkQ,WAAA,YAAAC,mBAAA,CAA5BD,WAAA,CAA8B7E,OAAO,UAAA8E,mBAAA,WAArCA,mBAAA,CAAuCC,aAAa,CAAE,CACzD;AACA1O,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAClC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,CAAEA,WAAW,CAAE0B,cAAc,CAAC,CAAC,CAE/D;AACAnE,SAAS,CAAC,IAAM,CACf,GAAI0D,kBAAkB,CAAE,KAAAoP,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAApD,eAAe,CAAGxL,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAgP,sBAAA,CAApBhP,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAqQ,sBAAA,WAAvCA,sBAAA,CAAyC9C,QAAQ,CAC5GlM,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACuN,QAAQ,EAAA+C,sBAAA,CAC9CjP,oBAAoB,CAAC,CAAC,CAAC,UAAAiP,sBAAA,iBAAvBA,sBAAA,CAAyB/C,QAAQ,CACpC,KAAM,CAAAoD,WAAW,CAAG7O,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAA0P,uBAAA,CAAd1P,cAAc,CAAEyC,SAAS,UAAAiN,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BvQ,WAAW,CAAG,CAAC,CAAC,UAAAwQ,uBAAA,iBAA5CA,uBAAA,CAA8CnF,OAAO,CACrDxK,cAAc,SAAdA,cAAc,kBAAA4P,uBAAA,CAAd5P,cAAc,CAAEyC,SAAS,UAAAmN,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCrF,OAAO,CAE1C;AACA;AACA,GAAIiC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAE8C,aAAa,CAAE,CACnC;AACA1O,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACT,kBAAkB,CAAEI,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,GAAI2D,kBAAkB,CAAE,KAAA0P,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAA3D,eAAe,CAAGxL,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAuP,sBAAA,CAApBvP,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA4Q,sBAAA,WAAvCA,sBAAA,CAAyCrD,QAAQ,CAC5GlM,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACuN,QAAQ,EAAAsD,sBAAA,CAC9CxP,oBAAoB,CAAC,CAAC,CAAC,UAAAwP,sBAAA,iBAAvBA,sBAAA,CAAyBtD,QAAQ,CACpC,KAAM,CAAAoD,WAAW,CAAG7O,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAiQ,uBAAA,CAAdjQ,cAAc,CAAEyC,SAAS,UAAAwN,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9Q,WAAW,CAAG,CAAC,CAAC,UAAA+Q,uBAAA,iBAA5CA,uBAAA,CAA8C1F,OAAO,CACrDxK,cAAc,SAAdA,cAAc,kBAAAmQ,uBAAA,CAAdnQ,cAAc,CAAEyC,SAAS,UAAA0N,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgC5F,OAAO,CAE1C;AACA,GAAIiC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAE8C,aAAa,CAAE,CACnC;AACA1O,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACR,kBAAkB,CAAEG,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAA2T,iBAAiB,CAAIC,CAAa,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAGtO,QAAQ,CAAC4G,cAAc,CAAC,cAAc,CAAC,CAE9D;AACA,GAAI0H,cAAc,EAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,CAAE,CAChE,OACD,CAEA;AACA;AACD,CAAC,CAEDxO,QAAQ,CAACiL,gBAAgB,CAAC,OAAO,CAAEmD,iBAAiB,CAAC,CAErD,MAAO,IAAM,CACZpO,QAAQ,CAACkL,mBAAmB,CAAC,OAAO,CAAEkD,iBAAiB,CAAC,CACzD,CAAC,CACF,CAAC,CAAE,CAAC7P,oBAAoB,CAAC,CAAC,CAC1B;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAgU,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAI/O,UAAU,CAAC0B,OAAO,CAAE,CACvB;AACA1B,UAAU,CAAC0B,OAAO,CAACkF,KAAK,CAAC3E,MAAM,CAAG,MAAM,CACxC,KAAM,CAAA+M,aAAa,CAAGhP,UAAU,CAAC0B,OAAO,CAACuN,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpD3N,iBAAiB,CAAC4N,YAAY,CAAC,CAE/B;AACA,GAAI3N,YAAY,CAACE,OAAO,CAAE,CACzB;AACA,GAAIF,YAAY,CAACE,OAAO,CAAC0N,YAAY,CAAE,CACtC5N,YAAY,CAACE,OAAO,CAAC0N,YAAY,CAAC,CAAC,CACpC,CACA;AACAC,UAAU,CAAC,IAAM,CAChB,GAAI7N,YAAY,CAACE,OAAO,EAAIF,YAAY,CAACE,OAAO,CAAC0N,YAAY,CAAE,CAC9D5N,YAAY,CAACE,OAAO,CAAC0N,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDL,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAO,QAAQ,CAAG,CAChBD,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CACjCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAQ,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAIxP,UAAU,CAAC0B,OAAO,EAAIgC,MAAM,CAAC+L,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCJ,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFQ,cAAc,CAACG,OAAO,CAAC1P,UAAU,CAAC0B,OAAO,CAAC,CAC3C,CAGA,GAAI1B,UAAU,CAAC0B,OAAO,EAAIgC,MAAM,CAACiM,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7CN,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,gBAAgB,CAACE,OAAO,CAAC1P,UAAU,CAAC0B,OAAO,CAAE,CAC5CkO,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC,CAC9B,GAAIV,cAAc,CAAE,CACnBA,cAAc,CAACW,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIV,gBAAgB,CAAE,CACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAC1S,WAAW,CAAC,CAAC,CACjB;AACA;AAEA,QAAS,CAAA2S,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CACA,KAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAA3J,QAAgB,CAAA4J,SAAA,CAAAxN,MAAA,IAAAwN,SAAA,MAAA7J,SAAA,CAAA6J,SAAA,IAAG,eAAe,CAC5D,OAAQ5J,QAAQ,EACf,IAAK,aAAa,CACjB,MAAO,CAAEtF,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,cAAc,CAClB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,aAAa,CACjB,MAAO,CAAEA,GAAG,CAAEjE,QAAQ,GAAK,EAAE,CAAG,gBAAgB,CAAG,gBAAiB,CAAC,CACtE,IAAK,cAAc,CAClB,MAAO,CAAEiE,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,UAAU,CACd,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,WAAW,CACf,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,YAAY,CAChB,MAAO,CAAEA,GAAG,CAAE,eAAgB,CAAC,CAChC,QACC,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CAClC,CACD,CAAC,CAEA;AACD,KAAM,CAAAmP,kBAAkB,CAAGA,CAACC,QAAgB,CAAE1F,eAAoB,CAAEqD,WAAgB,GAAK,CACxF,GAAI7O,oBAAoB,GAAK,SAAS,CAAE,CACvC;AACA,OAAQkR,QAAQ,EACf,IAAK,gBAAgB,CACpB,MAAO,CAAArC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsC,cAAc,IAAKhK,SAAS,CAAG0H,WAAW,CAACsC,cAAc,CAAG3F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2F,cAAc,CAChH,IAAK,eAAe,CACnB;AACA,MAAO,CAAAtC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuC,4BAA4B,IAAKjK,SAAS,CAAG0H,WAAW,CAACuC,4BAA4B,CAAG5F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE4F,4BAA4B,CAC1J,IAAK,UAAU,CACd,MAAO,CAAAvC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwC,QAAQ,IAAKlK,SAAS,CAAG0H,WAAW,CAACwC,QAAQ,CAAG7F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6F,QAAQ,CAC9F,IAAK,eAAe,CACnB,MAAO,CAAAxC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEP,aAAa,IAAKnH,SAAS,CAAG0H,WAAW,CAACP,aAAa,CAAG9C,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE8C,aAAa,CAC7G,QACC,MAAO,CAAA9C,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG0F,QAAQ,CAAC,CACpC,CACD,CAAC,IAAM,CACN;AACA,GAAIA,QAAQ,GAAK,eAAe,CAAE,CACjC,MAAO,CAAA1F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE4F,4BAA4B,CACrD,CACA,MAAO,CAAA5F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG0F,QAAQ,CAAC,CACnC,CACD,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAGA,CAAC1Q,OAAY,CAAE4K,eAAoB,CAAEqD,WAAgB,CAAE9M,IAAS,CAAED,GAAQ,GAAK,CACzGlB,OAAO,CAAC0G,KAAK,CAACF,QAAQ,CAAG,UAAU,CACnCxG,OAAO,CAAC0G,KAAK,CAACvF,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChCnB,OAAO,CAAC0G,KAAK,CAACxF,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC9BlB,OAAO,CAAC0G,KAAK,CAAC7E,KAAK,CAAG,GAAG+I,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+F,IAAI,IAAI,CAAE;AACpD3Q,OAAO,CAAC0G,KAAK,CAAC3E,MAAM,CAAG,GAAG6I,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+F,IAAI,IAAI,CACnD3Q,OAAO,CAAC0G,KAAK,CAACC,eAAe,CAAGiE,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgG,KAAK,CACtD5Q,OAAO,CAAC0G,KAAK,CAACiF,YAAY,CAAG,KAAK,CAClC3L,OAAO,CAAC0G,KAAK,CAACmK,MAAM,CAAG,iBAAiB,CAAE;AAC1C7Q,OAAO,CAAC0G,KAAK,CAACoK,UAAU,CAAG,MAAM,CACjC9Q,OAAO,CAAC0G,KAAK,CAACqK,aAAa,CAAG,MAAM,CAAE;AACtC/Q,OAAO,CAACgR,SAAS,CAAG,EAAE,CAEtB,GAAI,CAAApG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEqG,IAAI,IAAK,MAAM,EAAI,CAAArG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEqG,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAG9Q,QAAQ,CAAC+Q,aAAa,CAAC,MAAM,CAAC,CAC/CD,QAAQ,CAACE,SAAS,CAAGxG,eAAe,CAACqG,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAACxK,KAAK,CAAC8C,KAAK,CAAG,OAAO,CAC9B0H,QAAQ,CAACxK,KAAK,CAAC2K,QAAQ,CAAG,MAAM,CAChCH,QAAQ,CAACxK,KAAK,CAACyC,UAAU,CAAG,MAAM,CAClC+H,QAAQ,CAACxK,KAAK,CAAC4C,SAAS,CAAGsB,eAAe,CAACqG,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAACxK,KAAK,CAACO,OAAO,CAAG,MAAM,CAC/BiK,QAAQ,CAACxK,KAAK,CAAC4K,UAAU,CAAG,QAAQ,CACpCJ,QAAQ,CAACxK,KAAK,CAAC6K,cAAc,CAAG,QAAQ,CACxCL,QAAQ,CAACxK,KAAK,CAAC7E,KAAK,CAAG,MAAM,CAC7BqP,QAAQ,CAACxK,KAAK,CAAC3E,MAAM,CAAG,MAAM,CAC9B/B,OAAO,CAACwR,WAAW,CAACN,QAAQ,CAAC,CAC9B,CAEA;AACA;AACA,KAAM,CAAAO,qBAAqB,CAAGpB,kBAAkB,CAAC,gBAAgB,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CAChG,KAAM,CAAAyD,WAAW,CAAGtS,oBAAoB,GAAK,SAAS,CAClDqS,qBAAqB,GAAK,KAAK,EAAI,CAACzR,OAAO,CAAC2R,aAAa,CACzD/G,eAAe,EAAI1L,gBAAgB,EAAI,CAACc,OAAO,CAAC2R,aAAc,CAElE,GAAID,WAAW,CAAE,CACP1R,OAAO,CAAC4R,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CACxC7R,OAAO,CAAC4R,SAAS,CAAC1K,MAAM,CAAC,yBAAyB,CAAC,CACvD,CAAC,IAAM,CACHlH,OAAO,CAAC4R,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3ClH,OAAO,CAAC4R,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACpD,CAEN;AACA7R,OAAO,CAAC0G,KAAK,CAACO,OAAO,CAAG,MAAM,CAC9BjH,OAAO,CAAC0G,KAAK,CAACqK,aAAa,CAAG,MAAM,CAEpC;AACA;AACA;AACA,KAAM,CAAAe,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CACvF,GAAI6D,aAAa,CAAE,CAClB9S,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACA;AAAA,CAGD;AACA;AACA,GAAI,CAACgB,OAAO,CAAC+R,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,KAAM,CAAAC,UAAU,CAAGhS,OAAO,CAACiS,SAAS,CAAC,IAAI,CAAgB,CACzD;AACA,GAAIjS,OAAO,CAAC2R,aAAa,GAAKpL,SAAS,CAAE,CAC9ByL,UAAU,CAASL,aAAa,CAAG3R,OAAO,CAAC2R,aAAa,CAC7D,CACN,GAAI3R,OAAO,CAACkS,UAAU,CAAE,CACvBlS,OAAO,CAACkS,UAAU,CAACC,YAAY,CAACH,UAAU,CAAEhS,OAAO,CAAC,CACpDA,OAAO,CAAGgS,UAAU,CACrB,CACD,CAEA;AACAhS,OAAO,CAAC0G,KAAK,CAACqK,aAAa,CAAG,MAAM,CAEpC;AACA,KAAM,CAAAqB,QAAQ,CAAG/B,kBAAkB,CAAC,UAAU,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CAC7E,KAAM,CAAAoE,WAAW,CAAI5D,CAAQ,EAAK,CACjCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CACnBvM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIoM,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACApT,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAAkU,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CAC9F,GAAIsE,oBAAoB,CAAE,CACzBvS,OAAO,CAAC4R,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3ClH,OAAO,CAAC4R,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChD7R,OAAO,CAAC2R,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED,KAAM,CAAAa,cAAc,CAAI/D,CAAQ,EAAK,CACpCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CAEnB;AACA;AACA,KAAM,CAAAR,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CACvF,GAAImE,QAAQ,GAAK,kBAAkB,EAAI,CAACN,aAAa,CAAE,CACtD;AAAA,CAEF,CAAC,CAED,KAAM,CAAAW,WAAW,CAAIhE,CAAQ,EAAK,CACjCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CACnBvM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIoM,QAAQ,GAAK,kBAAkB,EAAI,CAACA,QAAQ,CAAE,CACjD;AACApT,cAAc,CAAC,CAACC,WAAW,CAAC,CAE5B;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACJ,KAAM,CAAAiU,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CAC1F,GAAIsE,oBAAoB,CAAE,CACzBvS,OAAO,CAAC4R,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3ClH,OAAO,CAAC4R,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChD7R,OAAO,CAAC2R,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED;AACA,GAAI,CAAC3R,OAAO,CAAC+R,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,GAAIK,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACApS,OAAO,CAACqL,gBAAgB,CAAC,WAAW,CAAEgH,WAAW,CAAC,CAClDrS,OAAO,CAACqL,gBAAgB,CAAC,UAAU,CAAEmH,cAAc,CAAC,CAEpD;AACAxS,OAAO,CAACqL,gBAAgB,CAAC,OAAO,CAAEoH,WAAW,CAAC,CAC/C,CAAC,IAAM,CACN;AACAzS,OAAO,CAACqL,gBAAgB,CAAC,OAAO,CAAEoH,WAAW,CAAC,CAC/C,CAEA;AACAzS,OAAO,CAAC0S,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CACxD,CACD,CAAC,CACD7X,SAAS,CAAC,IAAM,CACf,GAAI,CAAAkG,OAAO,CACX,GAAI,CAAA4R,KAAK,CAET,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,MAAA,CAAAC,OAAA,CACH;AACAL,KAAK,CAAG,CAAAxU,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEyC,SAAS,GAAI,EAAE,CAEvC;AACA,KAAM,CAAAqS,WAAW,CAAG7T,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAA0U,uBAAA,CAAd1U,cAAc,CAAEyC,SAAS,UAAAiS,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4BvV,WAAW,CAAG,CAAC,CAAC,UAAAwV,uBAAA,WAA5CA,uBAAA,CAA8CjS,WAAW,CAC/G1C,cAAc,CAACyC,SAAS,CAACtD,WAAW,CAAG,CAAC,CAAC,CAASuD,WAAW,CAC9D,EAAAkS,MAAA,CAAAJ,KAAK,UAAAI,MAAA,kBAAAC,OAAA,CAALD,MAAA,CAAQ,CAAC,CAAC,UAAAC,OAAA,iBAAVA,OAAA,CAAYnS,WAAW,GAAI,EAAE,CAEhCE,OAAO,CAAGd,iBAAiB,CAACgT,WAAW,EAAI,EAAE,CAAC,CAC9CxT,gBAAgB,CAACsB,OAAO,CAAC,CAEzB,GAAIA,OAAO,CAAE,CACZ;AAAA,CAGD;AACA,KAAM,CAAAmS,iBAAiB,CAAGxU,gBAAgB,GAAK,SAAS,EACvDU,oBAAoB,GAAK,SAAS,EAClCrC,KAAK,GAAK,SAAS,EAClB2B,gBAAgB,GAAK,MAAM,EAAIU,oBAAoB,GAAK,SAAU,CAEpE,GAAI8T,iBAAiB,CAAE,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAItB;AACA,GAAI,CAAA5I,eAAe,CACnB,GAAI,CAAAqD,WAAW,CAEf,GAAI7O,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAwU,sBAAA,CAApBxU,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA6V,sBAAA,WAAvCA,sBAAA,CAAyCtI,QAAQ,CAAE,KAAA4I,uBAAA,CAAAC,uBAAA,CAC5F;AACA9I,eAAe,CAAGjM,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACuN,QAAQ,CAChEoD,WAAW,CAAG9P,cAAc,SAAdA,cAAc,kBAAAsV,uBAAA,CAAdtV,cAAc,CAAEyC,SAAS,UAAA6S,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BnW,WAAW,CAAG,CAAC,CAAC,UAAAoW,uBAAA,iBAA5CA,uBAAA,CAA8C/K,OAAO,CACpE,CAAC,IAAM,IAAIhK,oBAAoB,SAApBA,oBAAoB,YAAAyU,sBAAA,CAApBzU,oBAAoB,CAAG,CAAC,CAAC,UAAAyU,sBAAA,WAAzBA,sBAAA,CAA2BvI,QAAQ,CAAE,KAAA8I,uBAAA,CAAAC,uBAAA,CAC/C;AACAhJ,eAAe,CAAGjM,oBAAoB,CAAC,CAAC,CAAC,CAACkM,QAAQ,CAClDoD,WAAW,CAAG9P,cAAc,SAAdA,cAAc,kBAAAwV,uBAAA,CAAdxV,cAAc,CAAEyC,SAAS,UAAA+S,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCjL,OAAO,CACtD,CAAC,IAAM,KAAAkL,uBAAA,CAAAC,uBAAA,CACN;AACAlJ,eAAe,CAAG,CACjBG,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdiG,IAAI,CAAE,UAAU,CAChBL,KAAK,CAAE,QAAQ,CACfD,IAAI,CAAE,IAAI,CACVJ,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5B/C,aAAa,CAAE,KAChB,CAAC,CACDO,WAAW,CAAG,CAAA9P,cAAc,SAAdA,cAAc,kBAAA0V,uBAAA,CAAd1V,cAAc,CAAEyC,SAAS,UAAAiT,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BvW,WAAW,CAAG,CAAC,CAAC,UAAAwW,uBAAA,iBAA5CA,uBAAA,CAA8CnL,OAAO,GAAI,CAAC,CAAC,CAC1E,CACA,KAAM,CAAAvE,OAAO,CAAG0G,UAAU,CAAC,EAAAuI,gBAAA,CAAAzI,eAAe,UAAAyI,gBAAA,iBAAfA,gBAAA,CAAiBtI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA1G,OAAO,CAAGyG,UAAU,CAAC,EAAAwI,iBAAA,CAAA1I,eAAe,UAAA0I,iBAAA,iBAAfA,iBAAA,CAAiBtI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA+I,kBAAkB,CAAGjJ,UAAU,CAAC,EAAAyI,iBAAA,CAAA3I,eAAe,UAAA2I,iBAAA,iBAAfA,iBAAA,CAAiB5C,IAAI,GAAI,IAAI,CAAC,CAEpE;AACA9Q,cAAc,CAACkU,kBAAkB,CAAC,CAElC,GAAI,CAAA5S,IAAI,CAAED,GAAG,CACb,GAAIH,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CE,IAAI,CAAGH,IAAI,CAACuD,CAAC,CAAGH,OAAO,CACvBlD,GAAG,CAAGF,IAAI,CAACyD,CAAC,EAAIJ,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAG1C,IAAI,CAACqS,GAAG,CAAC3P,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAA4G,QAAQ,CAAG9G,sBAAsB,CAACnD,IAAI,CAAE+S,kBAAkB,CAAE3P,OAAO,CAAEC,OAAO,CAAC,CACnF1E,gBAAgB,CAACsL,QAAQ,CAAC,CAC3B,CAEA;AACA,KAAM,CAAAlE,eAAe,CAAG3G,QAAQ,CAAC4G,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpB/G,OAAO,CAAG+G,eAAe,CACzB;AACD,CAAC,IAAM,CACN;AACA/G,OAAO,CAAGI,QAAQ,CAAC+Q,aAAa,CAAC,KAAK,CAAC,CACvCnR,OAAO,CAACiU,EAAE,CAAG,cAAc,CAAE;AAC7BjU,OAAO,CAAC2R,aAAa,CAAG,KAAK,CAAE;AAC/BvR,QAAQ,CAAC8T,IAAI,CAAC1C,WAAW,CAACxR,OAAO,CAAC,CACnC,CAEAA,OAAO,CAAC0G,KAAK,CAACyN,MAAM,CAAG,SAAS,CAChCnU,OAAO,CAAC0G,KAAK,CAACqK,aAAa,CAAG,MAAM,CAAE;AAEtC;AACA/Q,OAAO,CAAC0G,KAAK,CAACmK,MAAM,CAAG,MAAM,CAE7B;AACA,IAAA2C,iBAAA,CAAI5I,eAAe,UAAA4I,iBAAA,WAAfA,iBAAA,CAAiB9F,aAAa,CAAE,CACnC1O,cAAc,CAAC,IAAI,CAAC,CACrB,CAEA;AACA0R,kBAAkB,CAAC1Q,OAAO,CAAE4K,eAAe,CAAEqD,WAAW,CAAE9M,IAAI,CAAED,GAAG,CAAC,CAEpE;AACA,KAAM,CAAA4Q,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEzF,eAAe,CAAEqD,WAAW,CAAC,CACvF,GAAI6D,aAAa,CAAE,CAClB9S,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AAAA,CAGD;AACD,CACD,CAAE,MAAOoV,KAAK,CAAE,CACfrO,OAAO,CAACqO,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD,CACD,CAAC,CAEDxB,iBAAiB,CAAC,CAAC,CAEnB,MAAO,IAAM,CACZ,KAAM,CAAA7L,eAAe,CAAG3G,QAAQ,CAAC4G,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACsN,OAAO,CAAG,IAAI,CAC9BtN,eAAe,CAACuN,WAAW,CAAG,IAAI,CAClCvN,eAAe,CAACwN,UAAU,CAAG,IAAI,CAClC,CACD,CAAC,CACF,CAAC,CAAE,CACFpW,cAAc,CACdQ,oBAAoB,CACpBJ,kBAAkB,CAClBC,kBAAkB,CAClBY,oBAAoB,CACpB9B,WACA;AAAA,CACA,CAAC,CACF,KAAM,CAAAkX,cAAc,CAAG,CAAArW,cAAc,SAAdA,cAAc,kBAAAzB,uBAAA,CAAdyB,cAAc,CAAEyC,SAAS,UAAAlE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgC8X,OAAO,UAAA7X,uBAAA,iBAAvCA,uBAAA,CAAyC8X,cAAc,GAAI,KAAK,CAEvF,QAAS,CAAAC,mBAAmBA,CAACtV,cAAmB,CAAE,KAAAuV,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACjD,GAAIzV,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAlB,cAAc,SAAdA,cAAc,kBAAAyW,uBAAA,CAAdzW,cAAc,CAAEyC,SAAS,UAAAgU,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCJ,OAAO,UAAAK,uBAAA,iBAAvCA,uBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAACtV,cAAc,CAAC,CAC5D,KAAM,CAAA4V,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACT,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAIQ,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACCrZ,IAAA,CAACP,aAAa,EACb8Z,OAAO,CAAC,MAAM,CACdvC,KAAK,CAAEpV,UAAW,CAClBiJ,QAAQ,CAAC,QAAQ,CACjB2O,UAAU,CAAE7X,WAAW,CAAG,CAAE,CAC5B8X,EAAE,CAAE,CACHzO,eAAe,CAAE,aAAa,CAC9BH,QAAQ,CAAE,oBAAoB,CAC9B,+BAA+B,CAAE,CAChCG,eAAe,CAAErH,aAAe;AACjC,CACD,CAAE,CACF+V,UAAU,cAAE1Z,IAAA,CAACV,MAAM,EAACyL,KAAK,CAAE,CAAE4O,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAE5Z,IAAA,CAACV,MAAM,EAACyL,KAAK,CAAE,CAAE4O,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACA,GAAIN,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCrZ,IAAA,CAACX,GAAG,EAACoa,EAAE,CAAE,CAAEnO,OAAO,CAAE,MAAM,CAAEqK,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAEtJ,OAAO,CAAE,KAAM,CAAE,CAAAuJ,QAAA,CAGrGC,KAAK,CAACC,IAAI,CAAC,CAAEhT,MAAM,CAAErF,UAAW,CAAC,CAAC,CAAC2K,GAAG,CAAC,CAAC2N,CAAC,CAAEC,KAAK,gBAChDna,IAAA,QAEC+K,KAAK,CAAE,CACN7E,KAAK,CAAE,MAAM,CACbE,MAAM,CAAE,KAAK,CACb4E,eAAe,CAAEmP,KAAK,GAAKxY,WAAW,CAAG,CAAC,CAAGgC,aAAa,CAAG,SAAS,CAAE;AACxEqM,YAAY,CAAE,OACf,CAAE,EANGmK,KAOL,CACD,CAAC,CACE,CAAC,CAER,CACA,GAAId,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCrZ,IAAA,CAACX,GAAG,EAACoa,EAAE,CAAE,CAAEnO,OAAO,CAAE,MAAM,CAAEqK,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,YAAa,CAAE,CAAAE,QAAA,cAC9E7Z,KAAA,CAACP,UAAU,EAAC8Z,EAAE,CAAE,CAAEjJ,OAAO,CAAE,KAAK,CAAE3C,KAAK,CAAElK,aAAc,CAAE,CAAAoW,QAAA,EAAC,OACpD,CAACpY,WAAW,CAAC,MAAI,CAACC,UAAU,EACtB,CAAC,CACT,CAAC,CAER,CAEA,GAAIyX,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACCrZ,IAAA,CAACX,GAAG,EAAA0a,QAAA,cACH/Z,IAAA,CAACL,UAAU,EAAC4Z,OAAO,CAAC,OAAO,CAAAQ,QAAA,cAC1B/Z,IAAA,CAACR,cAAc,EACd+Z,OAAO,CAAC,aAAa,CACrBa,KAAK,CAAEtY,QAAS,CAChB2X,EAAE,CAAE,CACHrT,MAAM,CAAE,KAAK,CACX4J,YAAY,CAAE,MAAM,CACpBqK,MAAM,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC3BrP,eAAe,CAAErH,aAAe;AACjC,CACD,CAAE,CACF,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCzD,KAAA,CAAAE,SAAA,EAAA2Z,QAAA,EACElW,aAAa,eACb7D,IAAA,QAAA+Z,QAAA,CAcEzW,WAAW,eACXpD,KAAA,CAACR,OAAO,EACP6R,IAAI,CAAE+I,OAAO,CAACvW,aAAa,CAAC,EAAIuW,OAAO,CAACpZ,QAAQ,CAAE,CAClDA,QAAQ,CAAEA,QAAS,CACnBK,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACF2L,YAAY,CAAEA,YAAa,CAC3BG,eAAe,CAAEA,eAAgB,CACjCkN,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CACbzW,aAAa,CACV,CACAwB,GAAG,CAAExB,aAAa,CAACwB,GAAG,EAAI4J,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAAG,CAAC+L,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAAG4C,IAAI,CAACqS,GAAG,CAAClJ,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAAC,CAAC,CAC7IoC,IAAI,CAAEzB,aAAa,CAACyB,IAAI,CAAG2J,UAAU,CAAChM,YAAY,EAAI,GAAG,CACzD,CAAC,CACDyH,SACH,CACD6O,EAAE,CAAE,CACH;AACA;AACA;AACA,gBAAgB,CAAEvY,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CgU,MAAM,CAAE,IAAI,CACZ;AACA,GAAGnF,WAAW,CACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAGyE,iBAAiB,CAAC,CAAArS,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmL,QAAQ,GAAI,eAAe,CAAC,CACnE/H,GAAG,CAAE,GAAG,CAAC,CAAAxB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEwB,GAAG,GAAI,CAAC,GAC5BnC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC3C+L,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAClC,CAAC+L,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAChC4C,IAAI,CAACqS,GAAG,CAAClJ,UAAU,CAAC/L,YAAY,EAAI,GAAG,CAAC,CAAC,CAC1C,CAAC,CAAC,eAAe,CACrBoC,IAAI,CAAE,GAAG,CAAC,CAAAzB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEyB,IAAI,GAAI,CAAC,GAAKrC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC9EgM,UAAU,CAAChM,YAAY,CAAC,EAAI,CAAC,CAC9B,CAAC,CAAE,eAAe,CACrBsX,QAAQ,CAAE,QAAQ,CAClB;AACAtF,UAAU,CAAE,yKACb,CACD,CAAE,CACFuF,iBAAiB,CAAE,IAAK,CAAAX,QAAA,eAExB/Z,IAAA,QAAK+K,KAAK,CAAE,CAAE8O,YAAY,CAAE,KAAK,CAAEvO,OAAO,CAAE,MAAO,CAAE,CAAAyO,QAAA,CACnD,CAAA7X,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyY,aAAa,gBAC9B3a,IAAA,CAACT,UAAU,EACVqb,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFnB,EAAE,CAAE,CACH5O,QAAQ,CAAE,OAAO,CACjB6F,SAAS,CAAE,iCAAiC,CAC5ClL,IAAI,CAAE,MAAM,CACZ8C,KAAK,CAAE,MAAM,CACb+R,MAAM,CAAE,OAAO,CACfQ,UAAU,CAAE,iBAAiB,CAC7BlK,MAAM,CAAE,gBAAgB,CACxBuE,MAAM,CAAE,QAAQ,CAChBlF,YAAY,CAAE,MAAM,CACpBQ,OAAO,CAAE,gBACV,CAAE,CAAAuJ,QAAA,cAEF/Z,IAAA,CAACJ,SAAS,EAAC6Z,EAAE,CAAE,CAAEqB,IAAI,CAAE,CAAC,CAAEjN,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClC,CACZ,CACG,CAAC,cACN7N,IAAA,CAACF,gBAAgB,EAEpBib,GAAG,CAAEpV,YAAa,CAClBoF,KAAK,CAAE,CAAEiQ,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAE,CACRC,eAAe,CAAE,CAACzV,cAAc,CAChC0V,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAAzB,QAAA,cAEC/Z,IAAA,QAAK+K,KAAK,CAAE,CACXiQ,SAAS,CAAE,OAAO,CAClBP,QAAQ,CAAE,aACX,CAAE,CAAAV,QAAA,cACD7Z,KAAA,CAACb,GAAG,EAAC0L,KAAK,CAAE,CACX;AACA3E,MAAM,CAAEwK,aACT,CAAE,CAAAmJ,QAAA,eACD7Z,KAAA,CAACb,GAAG,EACH0b,GAAG,CAAE5W,UAAW,CAChBmH,OAAO,CAAC,MAAM,CACdmQ,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACf9F,cAAc,CAAC,QAAQ,CACvB6D,EAAE,CAAE,CACHvT,KAAK,CAAE,MAAM,CACbsK,OAAO,CAAE,KAAK,CACdmL,SAAS,CAAE,YACZ,CAAE,CAAA5B,QAAA,EAED/X,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuK,GAAG,CAAEqP,SAAc,EACpCA,SAAS,CAAC/K,WAAW,CAACtE,GAAG,CAAC,CAACsP,SAAc,CAAEC,QAAgB,gBAC1D9b,IAAA,CAACX,GAAG,EAEH0c,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAACI,GAAI,CACnBC,GAAG,CAAEL,SAAS,CAACM,OAAO,EAAI,OAAQ,CAClC1C,EAAE,CAAE,CACHuB,SAAS,CAAEY,SAAS,CAACQ,cAAc,EAAIP,SAAS,CAACO,cAAc,EAAI,OAAO,CAC1ErO,SAAS,CAAE6N,SAAS,CAAC5N,SAAS,EAAI,QAAQ,CAC1CqO,SAAS,CAAER,SAAS,CAACS,GAAG,EAAI,SAAS,CACrC;AACAlW,MAAM,CAAE,GAAGyV,SAAS,CAAC/K,aAAa,EAAI,GAAG,IAAI,CAC7C+J,UAAU,CAAEgB,SAAS,CAACtL,eAAe,EAAI,SAAS,CAClD8J,MAAM,CAAE,QACT,CAAE,CACFO,OAAO,CAAEA,CAAA,GAAM,CACd,GAAIgB,SAAS,CAACW,SAAS,CAAE,CACxB,KAAM,CAAArL,SAAS,CAAG0K,SAAS,CAACW,SAAS,CACrC1U,MAAM,CAAC0J,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACFnG,KAAK,CAAE,CAAEyN,MAAM,CAAEoD,SAAS,CAACW,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAnB1D,GAAGX,SAAS,CAAChP,EAAE,IAAIkP,QAAQ,EAoBhC,CACD,CACF,CAAC,CAEA/Z,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEwK,GAAG,CACxB,CAACiQ,SAAc,CAAErC,KAAU,QAAAsC,qBAAA,CAAAC,sBAAA,OAC1B,CAAAF,SAAS,CAAClV,IAAI,eACbtH,IAAA,CAACL,UAAU,EACVgd,SAAS,CAAC,eAAe,CACG;AAC5BlD,EAAE,CAAE,CACH1L,SAAS,CAAE,EAAA0O,qBAAA,CAAAD,SAAS,CAAC/O,cAAc,UAAAgP,qBAAA,iBAAxBA,qBAAA,CAA0BG,UAAU,GAAIrP,SAAS,CAACQ,SAAS,CACtEF,KAAK,CAAE,EAAA6O,sBAAA,CAAAF,SAAS,CAAC/O,cAAc,UAAAiP,sBAAA,iBAAxBA,sBAAA,CAA0B5O,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7DgP,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvBtM,OAAO,CAAE,OAAO,CAChBuM,QAAQ,CAAE,YAAY,CACtBC,YAAY,CAAE,YAAY,CAC1BC,OAAO,CAAE,MACV,CAAE,CACFC,uBAAuB,CAAEjP,iBAAiB,CAACuO,SAAS,CAAClV,IAAI,CAAG;AAAA,EAXvDkV,SAAS,CAAC5P,EAAE,EAAIuN,KAYrB,CACD,EACH,CAAC,EACG,CAAC,CAELgD,MAAM,CAACC,IAAI,CAACxN,cAAc,CAAC,CAACrD,GAAG,CAAEsD,WAAW,OAAAwN,qBAAA,CAAAC,sBAAA,oBAC5Ctd,IAAA,CAACX,GAAG,EACH0b,GAAG,CAAE3W,kBAAmB,CAExBqV,EAAE,CAAE,CACHnO,OAAO,CAAE,MAAM,CACfsK,cAAc,CAAEtB,YAAY,EAAA+I,qBAAA,CAACzN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAwN,qBAAA,iBAA9BA,qBAAA,CAAgCrP,SAAS,CAAC,CACvE0N,QAAQ,CAAE,MAAM,CAChBrB,MAAM,CAAE,OAAO,CACfrP,eAAe,EAAAsS,sBAAA,CAAE1N,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAyN,sBAAA,iBAA9BA,sBAAA,CAAgC/M,eAAe,CAChEC,OAAO,CAAE,OAAO,CAChBtK,KAAK,CAAE,MACR,CAAE,CAAA6T,QAAA,CAEDnK,cAAc,CAACC,WAAW,CAAC,CAACtD,GAAG,CAAC,CAACG,MAAW,CAAEyN,KAAa,QAAAoD,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3D7d,IAAA,CAACV,MAAM,EAENsb,OAAO,CAAEA,CAAA,GAAM7J,kBAAkB,CAACrE,MAAM,CAACoR,YAAY,CAAE,CACvDvE,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHY,MAAM,CAAE,eAAe,CACvBrP,eAAe,CAAE,EAAAuS,qBAAA,CAAA7Q,MAAM,CAACqR,gBAAgB,UAAAR,qBAAA,iBAAvBA,qBAAA,CAAyBS,qBAAqB,GAAI,SAAS,CAC5EnQ,KAAK,CAAE,EAAA2P,sBAAA,CAAA9Q,MAAM,CAACqR,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBS,eAAe,GAAI,MAAM,CACzDtN,MAAM,CAAE,EAAA8M,sBAAA,CAAA/Q,MAAM,CAACqR,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBS,iBAAiB,GAAI,aAAa,CACnExI,QAAQ,CAAE,EAAAgI,sBAAA,CAAAhR,MAAM,CAACqR,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBS,QAAQ,GAAI,MAAM,CACrDjY,KAAK,CAAE,EAAAyX,sBAAA,CAAAjR,MAAM,CAACqR,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyB/O,KAAK,GAAI,MAAM,CAC/C4B,OAAO,CAAE,SAAS,CAClB4N,UAAU,CAAE,QAAQ,CACpBC,aAAa,CAAE,MAAM,CACrBrO,YAAY,CAAE,EAAA4N,sBAAA,CAAAlR,MAAM,CAACqR,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBU,YAAY,GAAI,KAAK,CAC5D5N,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACV1F,eAAe,CAAE,EAAA6S,sBAAA,CAAAnR,MAAM,CAACqR,gBAAgB,UAAAF,sBAAA,iBAAvBA,sBAAA,CAAyBG,qBAAqB,GAAI,SAAS,CAAE;AAC9EO,OAAO,CAAE,GAAG,CAAE;AACd7N,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAAqJ,QAAA,CAEDrN,MAAM,CAAC8R,UAAU,EAtBbrE,KAuBE,CAAC,EACT,CAAC,EArCGtK,WAsCD,CAAC,EACN,CAAC,EACE,CAAC,CAGD,CAAC,EAhIL,aAAapK,cAAc,EAiIV,CAAC,CAElBoT,cAAc,EAAIjX,UAAU,CAAC,CAAC,EAAImB,gBAAgB,GAAK,MAAM,eAAI/C,IAAA,CAACX,GAAG,EAAA0a,QAAA,CAAET,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EAC7F,CACT,CAEG,CACL,cAEDtZ,IAAA,UAAA+Z,QAAA,CACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACC,CAAC,EAEP,CAAC,CAEL,CAAC,CAED,cAAe,CAAA1Z,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}