{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\HotspotPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HotspotPreview = ({\n  anchorEl,\n  guideStep,\n  title,\n  text,\n  imageUrl,\n  onClose,\n  onPrevious,\n  onContinue,\n  videoUrl,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData,\n  hotspotProperties,\n  handleHotspotHover,\n  handleHotspotClick,\n  isHotspotPopupOpen,\n  showHotspotenduser\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide31, _savedGuideData$Guide32, _savedGuideData$Guide33;\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    toolTipGuideMetaData,\n    elementSelected,\n    axisData,\n    tooltipXaxis,\n    tooltipYaxis,\n    setOpenTooltip,\n    openTooltip,\n    pulseAnimationsH,\n    hotspotGuideMetaData,\n    selectedTemplateTour,\n    selectedOption,\n    ProgressColor\n  } = useDrawerStore(state => state);\n  const [targetElement, setTargetElement] = useState(null);\n  // State to track if the popover should be shown\n  // State for popup visibility is managed through openTooltip\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [hotspotSize, setHotspotSize] = useState(30); // Track hotspot size for dynamic popup positioning\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n  let hotspot;\n  const getElementByXPath = xpath => {\n    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n    const node = result.singleNodeValue;\n    if (node instanceof HTMLElement) {\n      return node;\n    } else if (node !== null && node !== void 0 && node.parentElement) {\n      return node.parentElement; // Return parent if it's a text node\n    } else {\n      return null;\n    }\n  };\n  let xpath;\n  if (savedGuideData) xpath = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.ElementPath;\n  const getElementPosition = xpath => {\n    const element = getElementByXPath(xpath || \"\");\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        //+ window.scrollY + yOffset, // Adjust for vertical scroll\n        left: rect.left // + window.scrollX + xOffset, // Adjust for horizontal scroll\n      };\n    }\n    return null;\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  // Function to get estimated popup dimensions based on actual content (enhanced to match TooltipPreview)\n  const getEstimatedPopupDimensions = () => {\n    // Try to get actual dimensions from content if available (similar to TooltipPreview approach)\n    if (contentRef.current) {\n      const contentRect = contentRef.current.getBoundingClientRect();\n      const actualWidth = Math.max(contentRect.width, 50); // Minimum width\n      const actualHeight = Math.max(contentRect.height + 100, 150); // Add padding for buttons/progress\n\n      // Apply width constraints similar to TooltipPreview's approach\n      const widthStyling = getWidthStyling();\n      let constrainedWidth = actualWidth;\n\n      // Check if using auto width (like TooltipPreview's 'auto !important')\n      if (widthStyling.width.includes('auto')) {\n        // For auto-width, respect maxWidth constraint (like TooltipPreview's maxWidth approach)\n        const maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\n        const maxWidth = parseInt(maxWidthValue) || 300;\n        constrainedWidth = Math.min(actualWidth, maxWidth);\n      } else {\n        // For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\n        const fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\n        constrainedWidth = parseInt(fixedWidthValue) || actualWidth;\n      }\n      return {\n        width: constrainedWidth,\n        height: actualHeight\n      };\n    }\n\n    // Fallback estimation when content ref is not available (enhanced to match TooltipPreview)\n    const widthStyling = getWidthStyling();\n    let estimatedWidth = 300; // Default fallback\n\n    // Check if using auto width (like TooltipPreview's 'auto !important')\n    if (widthStyling.width.includes('auto')) {\n      // For auto-width, estimate based on content type and respect maxWidth (like TooltipPreview)\n      const maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\n      const maxWidth = parseInt(maxWidthValue) || 300;\n\n      // Enhanced content-based width estimation (similar to TooltipPreview's approach)\n      let contentBasedWidth = 250; // Base estimate\n\n      // Adjust based on content types present (similar to TooltipPreview's content analysis)\n      if (textFieldProperties && textFieldProperties.length > 0) {\n        // Estimate text width based on content length (enhanced calculation)\n        const totalTextLength = textFieldProperties.reduce((sum, field) => sum + (field.Text ? field.Text.length : 0), 0);\n        const avgTextLength = totalTextLength / textFieldProperties.length;\n        // Use character-based width estimation similar to TooltipPreview\n        contentBasedWidth = Math.min(Math.max(avgTextLength * 7.5, 200), maxWidth);\n      }\n      if (imageProperties && imageProperties.length > 0) {\n        // Images typically need more width (similar to TooltipPreview's image handling)\n        contentBasedWidth = Math.max(contentBasedWidth, 280);\n      }\n      if (customButton && customButton.length > 0) {\n        // Buttons may need additional width (enhanced button width calculation)\n        const buttonWidth = customButton.length * 85; // Slightly larger estimate for better fit\n        contentBasedWidth = Math.max(contentBasedWidth, Math.min(buttonWidth + 50, maxWidth));\n      }\n      estimatedWidth = Math.min(contentBasedWidth, maxWidth);\n    } else {\n      // For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\n      const fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\n      estimatedWidth = parseInt(fixedWidthValue) || 300;\n    }\n    const estimatedHeight = 250; // Standard height for all content types\n\n    return {\n      width: estimatedWidth,\n      height: estimatedHeight\n    };\n  };\n\n  // Function to calculate best position based on available space (similar to TooltipPreview)\n  const calculateBestPosition = elementRect => {\n    const viewportWidth = window.innerWidth;\n    const viewportHeight = window.innerHeight;\n    const spaceTop = elementRect.top;\n    const spaceBottom = viewportHeight - elementRect.bottom;\n    const spaceLeft = elementRect.left;\n    const spaceRight = viewportWidth - elementRect.right;\n    const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\n    if (maxSpace === spaceTop) return \"top\";\n    if (maxSpace === spaceBottom) return \"bottom\";\n    if (maxSpace === spaceLeft) return \"left\";\n    return \"right\";\n  };\n\n  // Function to calculate smart popup position that stays within viewport (enhanced with TooltipPreview logic)\n  const calculatePopupPosition = (elementRect, hotspotSize, xOffset, yOffset) => {\n    const hotspotLeft = elementRect.x + xOffset;\n    const hotspotTop = elementRect.y + yOffset;\n\n    // Get viewport dimensions\n    const viewportWidth = window.innerWidth;\n    const viewportHeight = window.innerHeight;\n\n    // Get estimated popup dimensions with enhanced width calculation\n    const {\n      width: popupWidth,\n      height: popupHeight\n    } = getEstimatedPopupDimensions();\n\n    // Viewport margin to ensure tooltip doesn't touch edges (similar to TooltipPreview's preventOverflow)\n    const VIEWPORT_MARGIN = 20;\n\n    // Gap between hotspot beacon and tooltip (similar to TooltipPreview's arrow spacing)\n    const TOOLTIP_GAP = 12;\n\n    // Smaller gap for tooltips positioned above hotspots (closer to beacon for better visual connection)\n    const TOOLTIP_GAP_ABOVE = 8;\n\n    // Handle very small viewports - reduce margins if popup is too large\n    const availableWidth = viewportWidth - VIEWPORT_MARGIN * 2;\n    const availableHeight = viewportHeight - VIEWPORT_MARGIN * 2;\n    const effectiveMargin = Math.min(VIEWPORT_MARGIN, Math.max(5, Math.min((availableWidth - popupWidth) / 2, (availableHeight - popupHeight) / 2)));\n\n    // Calculate hotspot beacon bounds (the visual indicator/marker)\n    const hotspotRight = hotspotLeft + hotspotSize;\n    const hotspotBottom = hotspotTop + hotspotSize;\n    const hotspotCenterX = hotspotLeft + hotspotSize / 2;\n    const hotspotCenterY = hotspotTop + hotspotSize / 2;\n\n    // Define possible positions in order of preference (enhanced with center-based positioning)\n    const positions = [\n    // Primary: bottom-center (positioned below hotspot beacon center)\n    {\n      name: 'bottom-center',\n      left: hotspotCenterX - popupWidth / 2,\n      top: hotspotBottom + TOOLTIP_GAP\n    },\n    // Secondary: top-center (positioned above hotspot beacon center)\n    {\n      name: 'top-center',\n      left: hotspotCenterX - popupWidth / 2,\n      top: hotspotTop - popupHeight - TOOLTIP_GAP_ABOVE\n    },\n    // Tertiary: right-center (positioned to the right of hotspot beacon center)\n    {\n      name: 'right-center',\n      left: hotspotRight + TOOLTIP_GAP,\n      top: hotspotCenterY - popupHeight / 2\n    },\n    // Quaternary: left-center (positioned to the left of hotspot beacon center)\n    {\n      name: 'left-center',\n      left: hotspotLeft - popupWidth - TOOLTIP_GAP,\n      top: hotspotCenterY - popupHeight / 2\n    },\n    // Fallback: bottom-right (original default)\n    {\n      name: 'bottom-right',\n      left: hotspotRight + TOOLTIP_GAP,\n      top: hotspotBottom + TOOLTIP_GAP\n    },\n    // Fallback: bottom-left\n    {\n      name: 'bottom-left',\n      left: hotspotLeft - popupWidth - TOOLTIP_GAP,\n      top: hotspotBottom + TOOLTIP_GAP\n    }];\n\n    // Function to check if a position fits within viewport\n    const isPositionValid = pos => {\n      const right = pos.left + popupWidth;\n      const bottom = pos.top + popupHeight;\n      return pos.left >= effectiveMargin && pos.top >= effectiveMargin && right <= viewportWidth - effectiveMargin && bottom <= viewportHeight - effectiveMargin;\n    };\n\n    // Find the first valid position\n    let selectedPosition = positions.find(pos => isPositionValid(pos));\n\n    // If no position fits perfectly, use the default and adjust to fit\n    if (!selectedPosition) {\n      selectedPosition = positions[0]; // Default: bottom-right\n\n      // Adjust horizontally if needed\n      if (selectedPosition.left + popupWidth > viewportWidth - effectiveMargin) {\n        selectedPosition.left = viewportWidth - popupWidth - effectiveMargin;\n      }\n      if (selectedPosition.left < effectiveMargin) {\n        selectedPosition.left = effectiveMargin;\n      }\n\n      // Adjust vertically if needed\n      if (selectedPosition.top + popupHeight > viewportHeight - effectiveMargin) {\n        selectedPosition.top = viewportHeight - popupHeight - effectiveMargin;\n      }\n      if (selectedPosition.top < effectiveMargin) {\n        selectedPosition.top = effectiveMargin;\n      }\n    }\n\n    // Enhanced debug logging for positioning issues (can be removed in production)\n    if (process.env.NODE_ENV === 'development') {\n      console.log('🎯 Smart positioning calculation:', {\n        elementRect: {\n          x: elementRect.x,\n          y: elementRect.y,\n          width: elementRect.width,\n          height: elementRect.height\n        },\n        hotspotPosition: {\n          left: hotspotLeft,\n          top: hotspotTop\n        },\n        hotspotSize: hotspotSize,\n        offsets: {\n          xOffset,\n          yOffset\n        },\n        popupDimensions: {\n          width: popupWidth,\n          height: popupHeight\n        },\n        viewport: {\n          width: viewportWidth,\n          height: viewportHeight\n        },\n        selectedPosition: selectedPosition.name,\n        finalPosition: {\n          left: selectedPosition.left,\n          top: selectedPosition.top\n        },\n        scrollOffset: {\n          x: window.scrollX,\n          y: window.scrollY\n        }\n      });\n    }\n    return {\n      top: selectedPosition.top + window.scrollY,\n      left: selectedPosition.left + window.scrollX\n    };\n  };\n  // Consolidated effect for handling element selection and positioning (fixed for reselection scenarios)\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    if (element) {\n      var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n      const rect = element.getBoundingClientRect();\n\n      // Use smart positioning logic instead of basic positioning\n      // Handle both regular hotspots and tour hotspots\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData2 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n      const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n      const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n      const currentHotspotSize = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size) || \"30\");\n\n      // Apply smart positioning for element reselection scenarios\n      const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n\n      // Debug logging for element reselection\n      if (process.env.NODE_ENV === 'development') {\n        console.log('🔄 Element reselection positioning:', {\n          trigger: 'xpath change',\n          selectedTemplateTour,\n          currentStep,\n          elementRect: rect,\n          hotspotProps: {\n            xOffset,\n            yOffset,\n            size: currentHotspotSize\n          },\n          popupPosition: popupPos\n        });\n      }\n      setPopupPosition(popupPos);\n    }\n  }, [xpath, toolTipGuideMetaData, selectedTemplateTour, currentStep]);\n\n  // Effect for handling guide step changes (also uses smart positioning)\n  useEffect(() => {\n    var _guideStep;\n    const element = getElementByXPath(guideStep === null || guideStep === void 0 ? void 0 : (_guideStep = guideStep[currentStep - 1]) === null || _guideStep === void 0 ? void 0 : _guideStep.ElementPath);\n    setTargetElement(element);\n    if (element) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      element.style.backgroundColor = \"red !important\";\n\n      // Use smart positioning logic for guide step changes\n      const rect = element.getBoundingClientRect();\n      // Handle both regular hotspots and tour hotspots\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData3 !== void 0 && _toolTipGuideMetaData3.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData4 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.hotspots;\n      const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n      const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n      const currentHotspotSize = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size) || \"30\");\n\n      // Apply smart positioning for guide step navigation\n      const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n\n      // Debug logging for guide step changes\n      if (process.env.NODE_ENV === 'development') {\n        console.log('📋 Guide step positioning:', {\n          trigger: 'guide step change',\n          selectedTemplateTour,\n          currentStep,\n          elementRect: rect,\n          hotspotProps: {\n            xOffset,\n            yOffset,\n            size: currentHotspotSize\n          },\n          popupPosition: popupPos\n        });\n      }\n      setPopupPosition(popupPos);\n    }\n  }, [guideStep, currentStep, toolTipGuideMetaData, selectedTemplateTour]);\n\n  // Hotspot styles are applied directly in the applyHotspotStyles function\n  // State for overlay value\n  const [, setOverlayValue] = useState(false);\n  const handleContinue = () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        setCurrentStep(currentStep + 1);\n        onContinue();\n        renderNextPopup(currentStep < totalSteps);\n      }\n    } else {\n      setCurrentStep(currentStep + 1);\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.style.display = \"none\";\n        existingHotspot.remove();\n      }\n    }\n  };\n  const renderNextPopup = shouldRenderNextPopup => {\n    var _savedGuideData$Guide3, _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11, _savedGuideData$Guide12;\n    return shouldRenderNextPopup ? /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: isHotspotPopupOpen,\n      showHotspotenduser: showHotspotenduser,\n      handleHotspotHover: handleHotspotHover,\n      handleHotspotClick: handleHotspotClick,\n      anchorEl: anchorEl,\n      savedGuideData: savedGuideData,\n      guideStep: guideStep,\n      onClose: onClose,\n      onPrevious: handlePrevious,\n      onContinue: handleContinue,\n      title: title,\n      text: text,\n      imageUrl: imageUrl,\n      currentStep: currentStep + 1,\n      totalSteps: totalSteps,\n      onDontShowAgain: onDontShowAgain,\n      progress: progress,\n      textFieldProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3[currentStep]) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4.TextFieldProperties,\n      imageProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5[currentStep]) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ImageProperties,\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[currentStep]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.ButtonSection) === null || _savedGuideData$Guide9 === void 0 ? void 0 : (_savedGuideData$Guide10 = _savedGuideData$Guide9.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide10 === void 0 ? void 0 : _savedGuideData$Guide10.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: modalProperties,\n      canvasProperties: canvasProperties,\n      htmlSnippet: htmlSnippet,\n      OverlayValue: OverlayValue,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide11 = savedGuideData.GuideStep) === null || _savedGuideData$Guide11 === void 0 ? void 0 : (_savedGuideData$Guide12 = _savedGuideData$Guide11[currentStep - 1]) === null || _savedGuideData$Guide12 === void 0 ? void 0 : _savedGuideData$Guide12.Hotspot) || {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 4\n    }, this) : null;\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  useEffect(() => {\n    if (OverlayValue) {\n      setOverlayValue(true);\n    } else {\n      setOverlayValue(false);\n    }\n  }, [OverlayValue]);\n  // Image fit is used directly in the component\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n\n  // Image styles are applied directly in the component\n\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Function to check if canvas width has been modified from default\n  const isCanvasWidthModified = () => {\n    var _canvasProperties$Wid;\n    const defaultWidth = \"300\"; // Default width without \"px\"\n    const currentWidth = (canvasProperties === null || canvasProperties === void 0 ? void 0 : (_canvasProperties$Wid = canvasProperties.Width) === null || _canvasProperties$Wid === void 0 ? void 0 : _canvasProperties$Wid.replace(\"px\", \"\")) || defaultWidth;\n    return currentWidth !== defaultWidth;\n  };\n\n  // Function to get width styling based on user preferences (enhanced to match TooltipPreview approach)\n  const getWidthStyling = () => {\n    // Check if user has modified canvas width from default\n    const hasCustomWidth = isCanvasWidthModified();\n    if (hasCustomWidth && canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width) {\n      // User-specified width: use exact width with !important, matching TooltipPreview's approach\n      const userWidth = canvasProperties.Width.includes('px') ? canvasProperties.Width : `${canvasProperties.Width}px`;\n      return {\n        width: `${userWidth} !important`,\n        // Use !important like TooltipPreview\n        maxWidth: `${userWidth} !important`,\n        // Override the default limit with !important\n        minWidth: 'unset' // Remove any minimum width constraints\n      };\n    } else {\n      // Default behavior: auto-adjust with content-based sizing, exactly like TooltipPreview\n      // TooltipPreview uses: width: 'auto !important', maxWidth: canvasStyle?.Width || \"300px\"\n      return {\n        width: 'auto !important',\n        // Match TooltipPreview's exact approach\n        maxWidth: '300px !important',\n        // Default maximum width constraint with !important\n        minWidth: 'unset' // Remove minimum width constraints for small content\n      };\n    }\n  };\n\n  // Update popup position when content changes (width is now calculated dynamically)\n  useEffect(() => {\n    // Recalculate popup position when content changes since width affects positioning\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        const rect = element.getBoundingClientRect();\n        // Handle both regular hotspots and tour hotspots\n        const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData6 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.hotspots;\n        const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n        const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n\n        // Debug logging for content changes\n        if (process.env.NODE_ENV === 'development') {\n          console.log('📝 Content change positioning:', {\n            trigger: 'content change',\n            popupPosition: popupPos\n          });\n        }\n        setPopupPosition(popupPos);\n      }\n    }\n  }, [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour]);\n\n  // Recalculate popup position when hotspot size changes or content dimensions change\n  useEffect(() => {\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        var _toolTipGuideMetaData7;\n        const rect = element.getBoundingClientRect();\n        const hotspotPropData = (_toolTipGuideMetaData7 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : _toolTipGuideMetaData7.hotspots;\n        const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n        const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n        setPopupPosition(popupPos);\n      }\n    }\n  }, [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton]);\n\n  // Recalculate popup position on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (xpath && hotspotSize) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          var _toolTipGuideMetaData8;\n          const rect = element.getBoundingClientRect();\n          const hotspotPropData = (_toolTipGuideMetaData8 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.hotspots;\n          const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n          const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n          setPopupPosition(popupPos);\n        }\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [xpath, hotspotSize, toolTipGuideMetaData]);\n  const groupedButtons = customButton.reduce((acc, button) => {\n    const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n    if (!acc[containerId]) {\n      acc[containerId] = [];\n    }\n    acc[containerId].push(button);\n    return acc;\n  }, {});\n  const widthStyling = getWidthStyling();\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Radius) || \"4px\",\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"black\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    // Apply padding similar to TooltipPreview approach\n    padding: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding) || \"10px\",\n    // Apply width styling with enhanced approach matching TooltipPreview\n    ...widthStyling,\n    // Add box shadow similar to TooltipPreview for consistency\n    boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\n    // Add border handling similar to TooltipPreview\n    border: canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.BorderSize && (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) !== \"0px\" ? `${canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize} solid ${(canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"transparent\"}` : \"none\"\n  };\n  const sectionHeight = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.SectionHeight) || \"auto\";\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"Previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide13, _savedGuideData$Guide14;\n        // Reset to the first step\n        setCurrentStep(1);\n        // If there's a specific URL for the first step, navigate to it\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide13 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide13 !== void 0 && (_savedGuideData$Guide14 = _savedGuideData$Guide13[0]) !== null && _savedGuideData$Guide14 !== void 0 && _savedGuideData$Guide14.ElementPath) {\n          const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    }\n    setOverlayValue(false);\n  };\n  useEffect(() => {\n    var _guideStep2, _guideStep2$Hotspot;\n    if (guideStep !== null && guideStep !== void 0 && (_guideStep2 = guideStep[currentStep - 1]) !== null && _guideStep2 !== void 0 && (_guideStep2$Hotspot = _guideStep2.Hotspot) !== null && _guideStep2$Hotspot !== void 0 && _guideStep2$Hotspot.ShowByDefault) {\n      // Show tooltip by default\n      setOpenTooltip(true);\n    }\n  }, [guideStep === null || guideStep === void 0 ? void 0 : guideStep[currentStep - 1], currentStep, setOpenTooltip]);\n\n  // Add effect to handle isHotspotPopupOpen prop changes\n  useEffect(() => {\n    if (isHotspotPopupOpen) {\n      var _toolTipGuideMetaData9, _toolTipGuideMetaData10, _savedGuideData$Guide15, _savedGuideData$Guide16, _savedGuideData$Guide17, _savedGuideData$Guide18;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData9 !== void 0 && _toolTipGuideMetaData9.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData10 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide15 = savedGuideData.GuideStep) === null || _savedGuideData$Guide15 === void 0 ? void 0 : (_savedGuideData$Guide16 = _savedGuideData$Guide15[currentStep - 1]) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide17 = savedGuideData.GuideStep) === null || _savedGuideData$Guide17 === void 0 ? void 0 : (_savedGuideData$Guide18 = _savedGuideData$Guide17[0]) === null || _savedGuideData$Guide18 === void 0 ? void 0 : _savedGuideData$Guide18.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      // For \"Hovering Hotspot\", we'll wait for the hover event\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [isHotspotPopupOpen, toolTipGuideMetaData]);\n\n  // Add effect to handle showHotspotenduser prop changes\n  useEffect(() => {\n    if (showHotspotenduser) {\n      var _toolTipGuideMetaData11, _toolTipGuideMetaData12, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData11 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData11 !== void 0 && _toolTipGuideMetaData11.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData12 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData12 === void 0 ? void 0 : _toolTipGuideMetaData12.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide19 = savedGuideData.GuideStep) === null || _savedGuideData$Guide19 === void 0 ? void 0 : (_savedGuideData$Guide20 = _savedGuideData$Guide19[currentStep - 1]) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : (_savedGuideData$Guide22 = _savedGuideData$Guide21[0]) === null || _savedGuideData$Guide22 === void 0 ? void 0 : _savedGuideData$Guide22.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [showHotspotenduser, toolTipGuideMetaData]);\n\n  // Add a global click handler to detect clicks outside the hotspot to close the tooltip\n  useEffect(() => {\n    const handleGlobalClick = e => {\n      const hotspotElement = document.getElementById(\"hotspotBlink\");\n\n      // Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\n      if (hotspotElement && hotspotElement.contains(e.target)) {\n        return;\n      }\n\n      // We want to keep the tooltip open once it's been displayed\n      // So we're not closing it on clicks outside anymore\n    };\n    document.addEventListener(\"click\", handleGlobalClick);\n    return () => {\n      document.removeEventListener(\"click\", handleGlobalClick);\n    };\n  }, [toolTipGuideMetaData]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  // We no longer need the persistent monitoring effect since we want the tooltip\n  // to close when the mouse leaves the hotspot\n\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const getCanvasPosition = (position = \"center-center\") => {\n    switch (position) {\n      case \"bottom-left\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-right\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-center\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"center-center\":\n        return {\n          top: \"25% !important\"\n        };\n      case \"left-center\":\n        return {\n          top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\"\n        };\n      case \"right-center\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-left\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-right\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-center\":\n        return {\n          top: \"9% !important\"\n        };\n      default:\n        return {\n          top: \"25% !important\"\n        };\n    }\n  };\n\n  // function to get the correct property value based on tour vs normal hotspot\n  const getHotspotProperty = (propName, hotspotPropData, hotspotData) => {\n    if (selectedTemplateTour === \"Hotspot\") {\n      // For tour hotspots, use saved data first, fallback to metadata\n      switch (propName) {\n        case 'PulseAnimation':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.PulseAnimation) !== undefined ? hotspotData.PulseAnimation : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.PulseAnimation;\n        case 'StopAnimation':\n          // Always use stopAnimationUponInteraction for consistency\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.stopAnimationUponInteraction) !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n        case 'ShowUpon':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowUpon) !== undefined ? hotspotData.ShowUpon : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon;\n        case 'ShowByDefault':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowByDefault) !== undefined ? hotspotData.ShowByDefault : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowByDefault;\n        default:\n          return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n      }\n    } else {\n      // For normal hotspots, use metadata\n      if (propName === 'StopAnimation') {\n        return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n      }\n      return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n    }\n  };\n  const applyHotspotStyles = (hotspot, hotspotPropData, hotspotData, left, top) => {\n    hotspot.style.position = \"absolute\";\n    hotspot.style.left = `${left}px`;\n    hotspot.style.top = `${top}px`;\n    hotspot.style.width = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`; // Default size if not provided\n    hotspot.style.height = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`;\n    hotspot.style.backgroundColor = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Color;\n    hotspot.style.borderRadius = \"50%\";\n    hotspot.style.zIndex = \"auto !important\"; // Increased z-index\n    hotspot.style.transition = \"none\";\n    hotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\n    hotspot.innerHTML = \"\";\n    if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n      const textSpan = document.createElement(\"span\");\n      textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n      textSpan.style.color = \"white\";\n      textSpan.style.fontSize = \"14px\";\n      textSpan.style.fontWeight = \"bold\";\n      textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n      textSpan.style.display = \"flex\";\n      textSpan.style.alignItems = \"center\";\n      textSpan.style.justifyContent = \"center\";\n      textSpan.style.width = \"100%\";\n      textSpan.style.height = \"100%\";\n      hotspot.appendChild(textSpan);\n    }\n\n    // Apply animation class if needed\n    // Track if pulse has been stopped by hover\n    const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\n    const shouldPulse = selectedTemplateTour === \"Hotspot\" ? pulseAnimationEnabled !== false && !hotspot._pulseStopped : hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped;\n    if (shouldPulse) {\n      hotspot.classList.add(\"pulse-animation\");\n      hotspot.classList.remove(\"pulse-animation-removed\");\n    } else {\n      hotspot.classList.remove(\"pulse-animation\");\n      hotspot.classList.add(\"pulse-animation-removed\");\n    }\n\n    // Ensure the hotspot is visible and clickable\n    hotspot.style.display = \"flex\";\n    hotspot.style.pointerEvents = \"auto\";\n\n    // No need for separate animation control functions here\n    // Animation will be controlled directly in the event handlers\n    // Set initial state of openTooltip based on ShowByDefault and ShowUpon\n    const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n    if (showByDefault) {\n      setOpenTooltip(true);\n    } else {\n      // If not showing by default, only show based on interaction type\n      //setOpenTooltip(false);\n    }\n\n    // Only clone and replace if the hotspot doesn't have event listeners already\n    // This prevents losing the _pulseStopped state unnecessarily\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      const newHotspot = hotspot.cloneNode(true);\n      // Copy the _pulseStopped property if it exists\n      if (hotspot._pulseStopped !== undefined) {\n        newHotspot._pulseStopped = hotspot._pulseStopped;\n      }\n      if (hotspot.parentNode) {\n        hotspot.parentNode.replaceChild(newHotspot, hotspot);\n        hotspot = newHotspot;\n      }\n    }\n\n    // Ensure pointer events are enabled\n    hotspot.style.pointerEvents = \"auto\";\n\n    // Define combined event handlers that handle both animation and tooltip\n    const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\n    const handleHover = e => {\n      e.stopPropagation();\n      console.log(\"Hover detected on hotspot\");\n\n      // Show tooltip if ShowUpon is \"Hovering Hotspot\"\n      if (showUpon === \"Hovering Hotspot\") {\n        // Set openTooltip to true when hovering\n        setOpenTooltip(true);\n\n        // Call the passed hover handler if it exists\n        if (typeof handleHotspotHover === \"function\") {\n          handleHotspotHover();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n    const handleMouseOut = e => {\n      e.stopPropagation();\n\n      // Hide tooltip when mouse leaves the hotspot\n      // Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\n      const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n      if (showUpon === \"Hovering Hotspot\" && !showByDefault) {\n        // setOpenTooltip(false);\n      }\n    };\n    const handleClick = e => {\n      e.stopPropagation();\n      console.log(\"Click detected on hotspot\");\n\n      // Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\n      if (showUpon === \"Clicking Hotspot\" || !showUpon) {\n        // Toggle the tooltip state\n        setOpenTooltip(!openTooltip);\n\n        // Call the passed click handler if it exists\n        if (typeof handleHotspotClick === \"function\") {\n          handleHotspotClick();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n\n    // Add appropriate event listeners based on ShowUpon property\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      if (showUpon === \"Hovering Hotspot\") {\n        // For hover interaction\n        hotspot.addEventListener(\"mouseover\", handleHover);\n        hotspot.addEventListener(\"mouseout\", handleMouseOut);\n\n        // Also add click handler for better user experience\n        hotspot.addEventListener(\"click\", handleClick);\n      } else {\n        // For click interaction (default)\n        hotspot.addEventListener(\"click\", handleClick);\n      }\n\n      // Mark that listeners have been attached\n      hotspot.setAttribute('data-listeners-attached', 'true');\n    }\n  };\n  useEffect(() => {\n    let element;\n    let steps;\n    const fetchGuideDetails = async () => {\n      try {\n        var _savedGuideData$Guide23, _savedGuideData$Guide24, _steps, _steps$;\n        //   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\n        steps = (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideStep) || [];\n\n        // For tour hotspots, use the current step's element path\n        const elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide23 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide23 !== void 0 && (_savedGuideData$Guide24 = _savedGuideData$Guide23[currentStep - 1]) !== null && _savedGuideData$Guide24 !== void 0 && _savedGuideData$Guide24.ElementPath ? savedGuideData.GuideStep[currentStep - 1].ElementPath : ((_steps = steps) === null || _steps === void 0 ? void 0 : (_steps$ = _steps[0]) === null || _steps$ === void 0 ? void 0 : _steps$.ElementPath) || \"\";\n        element = getElementByXPath(elementPath || \"\");\n        setTargetElement(element);\n        if (element) {\n          // element.style.outline = \"2px solid red\";\n        }\n\n        // Check if this is a hotspot scenario (normal or tour)\n        const isHotspotScenario = selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" || title === \"Hotspot\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\";\n        if (isHotspotScenario) {\n          var _toolTipGuideMetaData13, _toolTipGuideMetaData14, _hotspotPropData, _hotspotPropData2, _hotspotPropData3, _hotspotPropData4;\n          // Get hotspot properties - prioritize tour data for tour hotspots\n          let hotspotPropData;\n          let hotspotData;\n          if (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData13 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData13 !== void 0 && _toolTipGuideMetaData13.hotspots) {\n            var _savedGuideData$Guide25, _savedGuideData$Guide26;\n            // Tour hotspot - use current step metadata\n            hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide25 = savedGuideData.GuideStep) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25[currentStep - 1]) === null || _savedGuideData$Guide26 === void 0 ? void 0 : _savedGuideData$Guide26.Hotspot;\n          } else if (toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData14 = toolTipGuideMetaData[0]) !== null && _toolTipGuideMetaData14 !== void 0 && _toolTipGuideMetaData14.hotspots) {\n            var _savedGuideData$Guide27, _savedGuideData$Guide28;\n            // Normal hotspot - use first metadata entry\n            hotspotPropData = toolTipGuideMetaData[0].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide27 = savedGuideData.GuideStep) === null || _savedGuideData$Guide27 === void 0 ? void 0 : (_savedGuideData$Guide28 = _savedGuideData$Guide27[0]) === null || _savedGuideData$Guide28 === void 0 ? void 0 : _savedGuideData$Guide28.Hotspot;\n          } else {\n            var _savedGuideData$Guide29, _savedGuideData$Guide30;\n            // Fallback to default values for tour hotspots without metadata\n            hotspotPropData = {\n              XPosition: \"4\",\n              YPosition: \"4\",\n              Type: \"Question\",\n              Color: \"yellow\",\n              Size: \"16\",\n              PulseAnimation: true,\n              stopAnimationUponInteraction: true,\n              ShowUpon: \"Hovering Hotspot\",\n              ShowByDefault: false\n            };\n            hotspotData = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide29 = savedGuideData.GuideStep) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29[currentStep - 1]) === null || _savedGuideData$Guide30 === void 0 ? void 0 : _savedGuideData$Guide30.Hotspot) || {};\n          }\n          const xOffset = parseFloat(((_hotspotPropData = hotspotPropData) === null || _hotspotPropData === void 0 ? void 0 : _hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat(((_hotspotPropData2 = hotspotPropData) === null || _hotspotPropData2 === void 0 ? void 0 : _hotspotPropData2.YPosition) || \"4\");\n          const currentHotspotSize = parseFloat(((_hotspotPropData3 = hotspotPropData) === null || _hotspotPropData3 === void 0 ? void 0 : _hotspotPropData3.Size) || \"30\");\n\n          // Update hotspot size state\n          setHotspotSize(currentHotspotSize);\n          let left, top;\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            left = rect.x + xOffset;\n            top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\n\n            // Calculate popup position below the hotspot\n            const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n            setPopupPosition(popupPos);\n          }\n\n          // Check if hotspot already exists, preserve it to maintain _pulseStopped state\n          const existingHotspot = document.getElementById(\"hotspotBlink\");\n          if (existingHotspot) {\n            hotspot = existingHotspot;\n            // Don't reset _pulseStopped if it already exists\n          } else {\n            // Create new hotspot only if it doesn't exist\n            hotspot = document.createElement(\"div\");\n            hotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\n            hotspot._pulseStopped = false; // Set only on creation\n            document.body.appendChild(hotspot);\n          }\n          hotspot.style.cursor = \"pointer\";\n          hotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\n\n          // Make sure the hotspot is visible and clickable\n          hotspot.style.zIndex = \"9999\";\n\n          // If ShowByDefault is true, set openTooltip to true immediately\n          if ((_hotspotPropData4 = hotspotPropData) !== null && _hotspotPropData4 !== void 0 && _hotspotPropData4.ShowByDefault) {\n            setOpenTooltip(true);\n          }\n\n          // Set styles first\n          applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\n\n          // Set initial tooltip visibility based on ShowByDefault\n          const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n          if (showByDefault) {\n            setOpenTooltip(true);\n          } else {\n            //setOpenTooltip(false);\n          }\n\n          // We don't need to add event listeners here as they're already added in applyHotspotStyles\n        }\n      } catch (error) {\n        console.error(\"Error in fetchGuideDetails:\", error);\n      }\n    };\n    fetchGuideDetails();\n    return () => {\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.onclick = null;\n        existingHotspot.onmouseover = null;\n        existingHotspot.onmouseout = null;\n      }\n    };\n  }, [savedGuideData, toolTipGuideMetaData, isHotspotPopupOpen, showHotspotenduser, selectedTemplateTour, currentStep\n  // Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n  ]);\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide31 = savedGuideData.GuideStep) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31[0]) === null || _savedGuideData$Guide32 === void 0 ? void 0 : (_savedGuideData$Guide33 = _savedGuideData$Guide32.Tooltip) === null || _savedGuideData$Guide33 === void 0 ? void 0 : _savedGuideData$Guide33.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide34, _savedGuideData$Guide35, _savedGuideData$Guide36;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide34 = savedGuideData.GuideStep) === null || _savedGuideData$Guide34 === void 0 ? void 0 : (_savedGuideData$Guide35 = _savedGuideData$Guide34[0]) === null || _savedGuideData$Guide35 === void 0 ? void 0 : (_savedGuideData$Guide36 = _savedGuideData$Guide35.Tooltip) === null || _savedGuideData$Guide36 === void 0 ? void 0 : _savedGuideData$Guide36.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          position: \"inherit !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1357,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1358,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"14px\",\n            height: \"4px\",\n            backgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\",\n            // Active color and inactive color\n            borderRadius: \"100px\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1368,\n          columnNumber: 7\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1364,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1384,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1383,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              \"& .MuiLinearProgress-bar\": {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1395,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1394,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1393,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [targetElement && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: openTooltip && /*#__PURE__*/_jsxDEV(Popover, {\n        open: Boolean(popupPosition) || Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: () => {\n          // We want to keep the tooltip open once it's been displayed\n          // So we're not closing it on Popover close events\n        },\n        anchorOrigin: anchorOrigin,\n        transformOrigin: transformOrigin,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: popupPosition ? {\n          top: popupPosition.top + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\n          left: popupPosition.left + parseFloat(tooltipXaxis || \"0\")\n        } : undefined,\n        sx: {\n          // \"& .MuiBackdrop-root\": {\n          //     position: 'relative !important', // Ensures higher specificity\n          // },\n          \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n          '& .MuiPaper-root:not(.MuiMobileStepper-root)': {\n            zIndex: 1000,\n            // borderRadius: \"1px\",\n            ...canvasStyle,\n            //...getAnchorAndTransformOrigins,\n            //top: \"16% !important\",\n            // top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n            //     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n            //         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n            //             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n            //                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n            //                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n            //                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n            //                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n            //                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n            ...getCanvasPosition((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\"),\n            top: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.top) || 0) + (tooltipYaxis && tooltipYaxis != 'undefined' ? parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\")) : 0)}px !important`,\n            left: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.left) || 0) + (tooltipXaxis && tooltipXaxis != 'undefined' ? parseFloat(tooltipXaxis) || 0 : 0)}px !important`,\n            overflow: \"hidden\",\n            // Add smooth transitions for position changes (enhanced with user preference)\n            transition: 'top 0.25s cubic-bezier(0.4, 0, 0.2, 1), left 0.25s cubic-bezier(0.4, 0, 0.2, 1), width 0.25s cubic-bezier(0.4, 0, 0.2, 1), max-width 0.25s cubic-bezier(0.4, 0, 0.2, 1)'\n          }\n        },\n        disableScrollLock: true,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            placeContent: \"end\",\n            display: \"flex\"\n          },\n          children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              // Only close if explicitly requested by user clicking the close button\n              //setOpenTooltip(false);\n            },\n            sx: {\n              position: \"fixed\",\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n              left: \"auto\",\n              right: \"auto\",\n              margin: \"-15px\",\n              background: \"#fff !important\",\n              border: \"1px solid #ccc\",\n              zIndex: \"999999\",\n              borderRadius: \"50px\",\n              padding: \"5px !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              sx: {\n                zoom: 1,\n                color: \"#000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1507,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1489,\n            columnNumber: 10\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1487,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: \"400px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: \"400px\",\n              overflow: \"hidden auto\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                // Remove padding here since it's now handled in canvasStyle\n                height: sectionHeight\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                ref: contentRef,\n                display: \"flex\",\n                flexDirection: \"column\",\n                flexWrap: \"wrap\",\n                justifyContent: \"center\",\n                sx: {\n                  width: \"100%\",\n                  padding: \"8px\",\n                  boxSizing: \"border-box\"\n                },\n                children: [imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map(imageProp => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"img\",\n                  src: customImg.Url,\n                  alt: customImg.AltText || \"Image\",\n                  sx: {\n                    maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                    textAlign: imageProp.Alignment || \"center\",\n                    objectFit: customImg.Fit || \"contain\",\n                    //  width: \"500px\",\n                    height: `${customImg.SectionHeight || 250}px`,\n                    background: customImg.BackgroundColor || \"#ffffff\",\n                    margin: \"10px 0\"\n                  },\n                  onClick: () => {\n                    if (imageProp.Hyperlink) {\n                      const targetUrl = imageProp.Hyperlink;\n                      window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                    }\n                  },\n                  style: {\n                    cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                  }\n                }, `${imageProp.Id}-${imgIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1547,\n                  columnNumber: 13\n                }, this))), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n                  var _textField$TextProper, _textField$TextProper2;\n                  return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"qadpt-preview\",\n                    // Use a unique key, either Id or index\n                    sx: {\n                      textAlign: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextFormat) || textStyle.textAlign,\n                      color: ((_textField$TextProper2 = textField.TextProperties) === null || _textField$TextProper2 === void 0 ? void 0 : _textField$TextProper2.TextColor) || textStyle.color,\n                      whiteSpace: \"pre-wrap\",\n                      wordBreak: \"break-word\",\n                      padding: \"0 5px\",\n                      wordWrap: \"break-word\",\n                      overflowWrap: \"break-word\",\n                      hyphens: \"auto\"\n                    },\n                    dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n                  }, textField.Id || index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1575,\n                    columnNumber: 14\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1533,\n                columnNumber: 10\n              }, this), Object.keys(groupedButtons).map(containerId => {\n                var _groupedButtons$conta, _groupedButtons$conta2;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ref: buttonContainerRef,\n                  sx: {\n                    display: \"flex\",\n                    justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                    flexWrap: \"wrap\",\n                    margin: \"5px 0\",\n                    backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                    padding: \"5px 0\",\n                    width: \"100%\"\n                  },\n                  children: groupedButtons[containerId].map((button, index) => {\n                    var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6, _button$ButtonPropert7;\n                    return /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => handleButtonAction(button.ButtonAction),\n                      variant: \"contained\",\n                      sx: {\n                        margin: \"0 5px 5px 5px\",\n                        backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                        color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                        border: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"transparent\",\n                        fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"15px\",\n                        width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                        padding: \"4px 8px\",\n                        lineHeight: \"normal\",\n                        textTransform: \"none\",\n                        borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                        boxShadow: \"none !important\",\n                        // Remove box shadow in normal state\n                        \"&:hover\": {\n                          backgroundColor: ((_button$ButtonPropert7 = button.ButtonProperties) === null || _button$ButtonPropert7 === void 0 ? void 0 : _button$ButtonPropert7.ButtonBackgroundColor) || \"#007bff\",\n                          // Keep the same background color on hover\n                          opacity: 0.9,\n                          // Slightly reduce opacity on hover for visual feedback\n                          boxShadow: \"none !important\" // Remove box shadow in hover state\n                        }\n                      },\n                      children: button.ButtonName\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1609,\n                      columnNumber: 13\n                    }, this);\n                  })\n                }, containerId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1595,\n                  columnNumber: 11\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1529,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 8\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1511,\n          columnNumber: 8\n        }, this), enableProgress && totalSteps > 1 && selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n          children: renderProgress()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1643,\n          columnNumber: 75\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1432,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1417,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1650,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(HotspotPreview, \"mAtRG8V49snauW5GCmULZCO+LxU=\", false, function () {\n  return [useDrawerStore];\n});\n_c = HotspotPreview;\nexport default HotspotPreview;\nvar _c;\n$RefreshReg$(_c, \"HotspotPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HotspotPreview", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "element", "rect", "getBoundingClientRect", "top", "left", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "getEstimatedPopupDimensions", "current", "contentRect", "actualWidth", "Math", "max", "width", "actualHeight", "height", "widthStyling", "getWidthStyling", "constrained<PERSON>idth", "includes", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "replace", "parseInt", "min", "fixedWidthValue", "estimatedWidth", "contentBasedWidth", "length", "totalTextLength", "reduce", "sum", "field", "Text", "avgTextLength", "buttonWidth", "estimatedHeight", "calculateBestPosition", "elementRect", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "spaceTop", "spaceBottom", "bottom", "spaceLeft", "spaceRight", "right", "maxSpace", "calculatePopupPosition", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "popup<PERSON><PERSON><PERSON>", "popupHeight", "VIEWPORT_MARGIN", "TOOLTIP_GAP", "TOOLTIP_GAP_ABOVE", "availableWidth", "availableHeight", "<PERSON><PERSON><PERSON>gin", "hotspotRight", "hotspotBottom", "hotspotCenterX", "hotspotCenterY", "positions", "name", "isPositionValid", "pos", "selectedPosition", "find", "process", "env", "NODE_ENV", "console", "log", "hotspotPosition", "offsets", "popupDimensions", "viewport", "finalPosition", "scrollOffset", "scrollX", "scrollY", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "currentHotspotSize", "Size", "popupPos", "trigger", "hotspotProps", "size", "_guideStep", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "style", "backgroundColor", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "acc", "curr", "concat", "Hotspot", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAnchorAndTransformOrigins", "position", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "_match", "p1", "p2", "p3", "isCanvasWidthModified", "_canvasProperties$Wid", "defaultWidth", "currentWidth", "<PERSON><PERSON><PERSON>", "hasCustomWidth", "userWidth", "min<PERSON><PERSON><PERSON>", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "handleResize", "_toolTipGuideMetaData8", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "padding", "Padding", "boxShadow", "border", "sectionHeight", "CustomImage", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData11", "_toolTipGuideMetaData12", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "undefined", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData13", "_toolTipGuideMetaData14", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "abs", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "background", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "flexDirection", "flexWrap", "boxSizing", "imageProp", "customImg", "imgIndex", "component", "src", "Url", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "wordWrap", "overflowWrap", "hyphens", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to get estimated popup dimensions based on actual content (enhanced to match TooltipPreview)\r\n\tconst getEstimatedPopupDimensions = () => {\r\n\t\t// Try to get actual dimensions from content if available (similar to TooltipPreview approach)\r\n\t\tif (contentRef.current) {\r\n\t\t\tconst contentRect = contentRef.current.getBoundingClientRect();\r\n\t\t\tconst actualWidth = Math.max(contentRect.width, 50); // Minimum width\r\n\t\t\tconst actualHeight = Math.max(contentRect.height + 100, 150); // Add padding for buttons/progress\r\n\r\n\t\t\t// Apply width constraints similar to TooltipPreview's approach\r\n\t\t\tconst widthStyling = getWidthStyling();\r\n\t\t\tlet constrainedWidth = actualWidth;\r\n\r\n\t\t\t// Check if using auto width (like TooltipPreview's 'auto !important')\r\n\t\t\tif (widthStyling.width.includes('auto')) {\r\n\t\t\t\t// For auto-width, respect maxWidth constraint (like TooltipPreview's maxWidth approach)\r\n\t\t\t\tconst maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\t\tconst maxWidth = parseInt(maxWidthValue) || 300;\r\n\t\t\t\tconstrainedWidth = Math.min(actualWidth, maxWidth);\r\n\t\t\t} else {\r\n\t\t\t\t// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\r\n\t\t\t\tconst fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\t\tconstrainedWidth = parseInt(fixedWidthValue) || actualWidth;\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\twidth: constrainedWidth,\r\n\t\t\t\theight: actualHeight\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Fallback estimation when content ref is not available (enhanced to match TooltipPreview)\r\n\t\tconst widthStyling = getWidthStyling();\r\n\t\tlet estimatedWidth = 300; // Default fallback\r\n\r\n\t\t// Check if using auto width (like TooltipPreview's 'auto !important')\r\n\t\tif (widthStyling.width.includes('auto')) {\r\n\t\t\t// For auto-width, estimate based on content type and respect maxWidth (like TooltipPreview)\r\n\t\t\tconst maxWidthValue = widthStyling.maxWidth.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\tconst maxWidth = parseInt(maxWidthValue) || 300;\r\n\r\n\t\t\t// Enhanced content-based width estimation (similar to TooltipPreview's approach)\r\n\t\t\tlet contentBasedWidth = 250; // Base estimate\r\n\r\n\t\t\t// Adjust based on content types present (similar to TooltipPreview's content analysis)\r\n\t\t\tif (textFieldProperties && textFieldProperties.length > 0) {\r\n\t\t\t\t// Estimate text width based on content length (enhanced calculation)\r\n\t\t\t\tconst totalTextLength = textFieldProperties.reduce((sum: number, field: any) =>\r\n\t\t\t\t\tsum + (field.Text ? field.Text.length : 0), 0);\r\n\t\t\t\tconst avgTextLength = totalTextLength / textFieldProperties.length;\r\n\t\t\t\t// Use character-based width estimation similar to TooltipPreview\r\n\t\t\t\tcontentBasedWidth = Math.min(Math.max(avgTextLength * 7.5, 200), maxWidth);\r\n\t\t\t}\r\n\r\n\t\t\tif (imageProperties && imageProperties.length > 0) {\r\n\t\t\t\t// Images typically need more width (similar to TooltipPreview's image handling)\r\n\t\t\t\tcontentBasedWidth = Math.max(contentBasedWidth, 280);\r\n\t\t\t}\r\n\r\n\t\t\tif (customButton && customButton.length > 0) {\r\n\t\t\t\t// Buttons may need additional width (enhanced button width calculation)\r\n\t\t\t\tconst buttonWidth = customButton.length * 85; // Slightly larger estimate for better fit\r\n\t\t\t\tcontentBasedWidth = Math.max(contentBasedWidth, Math.min(buttonWidth + 50, maxWidth));\r\n\t\t\t}\r\n\r\n\t\t\testimatedWidth = Math.min(contentBasedWidth, maxWidth);\r\n\t\t} else {\r\n\t\t\t// For fixed width, use the specified width (like TooltipPreview's canvasStyle.Width)\r\n\t\t\tconst fixedWidthValue = widthStyling.width.replace(/[^\\d]/g, ''); // Extract numeric value\r\n\t\t\testimatedWidth = parseInt(fixedWidthValue) || 300;\r\n\t\t}\r\n\r\n\t\tconst estimatedHeight = 250; // Standard height for all content types\r\n\r\n\t\treturn {\r\n\t\t\twidth: estimatedWidth,\r\n\t\t\theight: estimatedHeight\r\n\t\t};\r\n\t};\r\n\r\n\t// Function to calculate best position based on available space (similar to TooltipPreview)\r\n\tconst calculateBestPosition = (elementRect: DOMRect): \"top\" | \"left\" | \"right\" | \"bottom\" => {\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\tconst spaceTop = elementRect.top;\r\n\t\tconst spaceBottom = viewportHeight - elementRect.bottom;\r\n\t\tconst spaceLeft = elementRect.left;\r\n\t\tconst spaceRight = viewportWidth - elementRect.right;\r\n\r\n\t\tconst maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\r\n\r\n\t\tif (maxSpace === spaceTop) return \"top\";\r\n\t\tif (maxSpace === spaceBottom) return \"bottom\";\r\n\t\tif (maxSpace === spaceLeft) return \"left\";\r\n\t\treturn \"right\";\r\n\t};\r\n\r\n\t// Function to calculate smart popup position that stays within viewport (enhanced with TooltipPreview logic)\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Get viewport dimensions\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\t// Get estimated popup dimensions with enhanced width calculation\r\n\t\tconst { width: popupWidth, height: popupHeight } = getEstimatedPopupDimensions();\r\n\r\n\t\t// Viewport margin to ensure tooltip doesn't touch edges (similar to TooltipPreview's preventOverflow)\r\n\t\tconst VIEWPORT_MARGIN = 20;\r\n\r\n\t\t// Gap between hotspot beacon and tooltip (similar to TooltipPreview's arrow spacing)\r\n\t\tconst TOOLTIP_GAP = 12;\r\n\r\n\t\t// Smaller gap for tooltips positioned above hotspots (closer to beacon for better visual connection)\r\n\t\tconst TOOLTIP_GAP_ABOVE = 8;\r\n\r\n\t\t// Handle very small viewports - reduce margins if popup is too large\r\n\t\tconst availableWidth = viewportWidth - (VIEWPORT_MARGIN * 2);\r\n\t\tconst availableHeight = viewportHeight - (VIEWPORT_MARGIN * 2);\r\n\t\tconst effectiveMargin = Math.min(VIEWPORT_MARGIN,\r\n\t\t\tMath.max(5, Math.min((availableWidth - popupWidth) / 2, (availableHeight - popupHeight) / 2))\r\n\t\t);\r\n\r\n\t\t// Calculate hotspot beacon bounds (the visual indicator/marker)\r\n\t\tconst hotspotRight = hotspotLeft + hotspotSize;\r\n\t\tconst hotspotBottom = hotspotTop + hotspotSize;\r\n\t\tconst hotspotCenterX = hotspotLeft + (hotspotSize / 2);\r\n\t\tconst hotspotCenterY = hotspotTop + (hotspotSize / 2);\r\n\r\n\t\t// Define possible positions in order of preference (enhanced with center-based positioning)\r\n\t\tconst positions = [\r\n\t\t\t// Primary: bottom-center (positioned below hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-center',\r\n\t\t\t\tleft: hotspotCenterX - (popupWidth / 2),\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Secondary: top-center (positioned above hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'top-center',\r\n\t\t\t\tleft: hotspotCenterX - (popupWidth / 2),\r\n\t\t\t\ttop: hotspotTop - popupHeight - TOOLTIP_GAP_ABOVE\r\n\t\t\t},\r\n\t\t\t// Tertiary: right-center (positioned to the right of hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'right-center',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotCenterY - (popupHeight / 2)\r\n\t\t\t},\r\n\t\t\t// Quaternary: left-center (positioned to the left of hotspot beacon center)\r\n\t\t\t{\r\n\t\t\t\tname: 'left-center',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotCenterY - (popupHeight / 2)\r\n\t\t\t},\r\n\t\t\t// Fallback: bottom-right (original default)\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-right',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Fallback: bottom-left\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-left',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\t// Function to check if a position fits within viewport\r\n\t\tconst isPositionValid = (pos: { left: number; top: number }) => {\r\n\t\t\tconst right = pos.left + popupWidth;\r\n\t\t\tconst bottom = pos.top + popupHeight;\r\n\r\n\t\t\treturn (\r\n\t\t\t\tpos.left >= effectiveMargin &&\r\n\t\t\t\tpos.top >= effectiveMargin &&\r\n\t\t\t\tright <= viewportWidth - effectiveMargin &&\r\n\t\t\t\tbottom <= viewportHeight - effectiveMargin\r\n\t\t\t);\r\n\t\t};\r\n\r\n\t\t// Find the first valid position\r\n\t\tlet selectedPosition = positions.find(pos => isPositionValid(pos));\r\n\r\n\t\t// If no position fits perfectly, use the default and adjust to fit\r\n\t\tif (!selectedPosition) {\r\n\t\t\tselectedPosition = positions[0]; // Default: bottom-right\r\n\r\n\t\t\t// Adjust horizontally if needed\r\n\t\t\tif (selectedPosition.left + popupWidth > viewportWidth - effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = viewportWidth - popupWidth - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.left < effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = effectiveMargin;\r\n\t\t\t}\r\n\r\n\t\t\t// Adjust vertically if needed\r\n\t\t\tif (selectedPosition.top + popupHeight > viewportHeight - effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = viewportHeight - popupHeight - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.top < effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = effectiveMargin;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Enhanced debug logging for positioning issues (can be removed in production)\r\n\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\tconsole.log('🎯 Smart positioning calculation:', {\r\n\t\t\t\telementRect: { x: elementRect.x, y: elementRect.y, width: elementRect.width, height: elementRect.height },\r\n\t\t\t\thotspotPosition: { left: hotspotLeft, top: hotspotTop },\r\n\t\t\t\thotspotSize: hotspotSize,\r\n\t\t\t\toffsets: { xOffset, yOffset },\r\n\t\t\t\tpopupDimensions: { width: popupWidth, height: popupHeight },\r\n\t\t\t\tviewport: { width: viewportWidth, height: viewportHeight },\r\n\t\t\t\tselectedPosition: selectedPosition.name,\r\n\t\t\t\tfinalPosition: { left: selectedPosition.left, top: selectedPosition.top },\r\n\t\t\t\tscrollOffset: { x: window.scrollX, y: window.scrollY }\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\ttop: selectedPosition.top + window.scrollY,\r\n\t\t\tleft: selectedPosition.left + window.scrollX\r\n\t\t};\r\n\t};\r\n\t// Consolidated effect for handling element selection and positioning (fixed for reselection scenarios)\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\r\n\t\t\t// Use smart positioning logic instead of basic positioning\r\n\t\t\t// Handle both regular hotspots and tour hotspots\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\r\n\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t// Apply smart positioning for element reselection scenarios\r\n\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\r\n\t\t\t// Debug logging for element reselection\r\n\t\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\t\tconsole.log('🔄 Element reselection positioning:', {\r\n\t\t\t\t\ttrigger: 'xpath change',\r\n\t\t\t\t\tselectedTemplateTour,\r\n\t\t\t\t\tcurrentStep,\r\n\t\t\t\t\telementRect: rect,\r\n\t\t\t\t\thotspotProps: { xOffset, yOffset, size: currentHotspotSize },\r\n\t\t\t\t\tpopupPosition: popupPos\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tsetPopupPosition(popupPos);\r\n\t\t}\r\n\t}, [xpath, toolTipGuideMetaData, selectedTemplateTour, currentStep]);\r\n\r\n\t// Effect for handling guide step changes (also uses smart positioning)\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Use smart positioning logic for guide step changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t// Handle both regular hotspots and tour hotspots\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\r\n\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t// Apply smart positioning for guide step navigation\r\n\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\r\n\t\t\t// Debug logging for guide step changes\r\n\t\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\t\tconsole.log('📋 Guide step positioning:', {\r\n\t\t\t\t\ttrigger: 'guide step change',\r\n\t\t\t\t\tselectedTemplateTour,\r\n\t\t\t\t\tcurrentStep,\r\n\t\t\t\t\telementRect: rect,\r\n\t\t\t\t\thotspotProps: { xOffset, yOffset, size: currentHotspotSize },\r\n\t\t\t\t\tpopupPosition: popupPos\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tsetPopupPosition(popupPos);\r\n\t\t}\r\n\t}, [guideStep, currentStep, toolTipGuideMetaData, selectedTemplateTour]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\r\n\r\n\t// Function to check if canvas width has been modified from default\r\n\tconst isCanvasWidthModified = () => {\r\n\t\tconst defaultWidth = \"300\"; // Default width without \"px\"\r\n\t\tconst currentWidth = canvasProperties?.Width?.replace(\"px\", \"\") || defaultWidth;\r\n\t\treturn currentWidth !== defaultWidth;\r\n\t};\r\n\r\n\t// Function to get width styling based on user preferences (enhanced to match TooltipPreview approach)\r\n\tconst getWidthStyling = () => {\r\n\t\t// Check if user has modified canvas width from default\r\n\t\tconst hasCustomWidth = isCanvasWidthModified();\r\n\r\n\t\tif (hasCustomWidth && canvasProperties?.Width) {\r\n\t\t\t// User-specified width: use exact width with !important, matching TooltipPreview's approach\r\n\t\t\tconst userWidth = canvasProperties.Width.includes('px')\r\n\t\t\t\t? canvasProperties.Width\r\n\t\t\t\t: `${canvasProperties.Width}px`;\r\n\t\t\treturn {\r\n\t\t\t\twidth: `${userWidth} !important`, // Use !important like TooltipPreview\r\n\t\t\t\tmaxWidth: `${userWidth} !important`, // Override the default limit with !important\r\n\t\t\t\tminWidth: 'unset' // Remove any minimum width constraints\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\t// Default behavior: auto-adjust with content-based sizing, exactly like TooltipPreview\r\n\t\t\t// TooltipPreview uses: width: 'auto !important', maxWidth: canvasStyle?.Width || \"300px\"\r\n\t\t\treturn {\r\n\t\t\t\twidth: 'auto !important', // Match TooltipPreview's exact approach\r\n\t\t\t\tmaxWidth: '300px !important', // Default maximum width constraint with !important\r\n\t\t\t\tminWidth: 'unset' // Remove minimum width constraints for small content\r\n\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t// Update popup position when content changes (width is now calculated dynamically)\r\n\tuseEffect(() => {\r\n\t\t// Recalculate popup position when content changes since width affects positioning\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t// Handle both regular hotspots and tour hotspots\r\n\t\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\r\n\t\t\t\t// Debug logging for content changes\r\n\t\t\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\t\t\tconsole.log('📝 Content change positioning:', {\r\n\t\t\t\t\t\ttrigger: 'content change',\r\n\t\t\t\t\t\tpopupPosition: popupPos\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData, selectedTemplateTour]);\r\n\r\n\t// Recalculate popup position when hotspot size changes or content dimensions change\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst widthStyling = getWidthStyling();\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\t// Apply padding similar to TooltipPreview approach\r\n\t\tpadding: canvasProperties?.Padding || \"10px\",\r\n\t\t// Apply width styling with enhanced approach matching TooltipPreview\r\n\t\t...widthStyling,\r\n\t\t// Add box shadow similar to TooltipPreview for consistency\r\n\t\tboxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\r\n\t\t// Add border handling similar to TooltipPreview\r\n\t\tborder: canvasProperties?.BorderSize && canvasProperties?.BorderSize !== \"0px\"\r\n\t\t\t? `${canvasProperties?.BorderSize} solid ${canvasProperties?.BorderColor || \"transparent\"}`\r\n\t\t\t: \"none\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left + parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t// Add smooth transitions for position changes (enhanced with user preference)\r\n\t\t\t\t\t\t\t\t\ttransition: 'top 0.25s cubic-bezier(0.4, 0, 0.2, 1), left 0.25s cubic-bezier(0.4, 0, 0.2, 1), width 0.25s cubic-bezier(0.4, 0, 0.2, 1), max-width 0.25s cubic-bezier(0.4, 0, 0.2, 1)',\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"400px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: \"hidden auto\"\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\t// Remove padding here since it's now handled in canvasStyle\r\n\t\t\t\t\t\t\t\t\theight: sectionHeight\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxSizing: \"border-box\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflowWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thyphens: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t\t\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,EAAEC,OAAO,EAAiBC,UAAU,QAAQ,eAAe;AAE1H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE;AACA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA6GrD,MAAMC,cAAoC,GAAGA,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC,cAAc;EACdC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,kBAAkB;EACnBC;AAEH,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IACLC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACD,CAAC,GAAG5D,cAAc,CAAE6D,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAqB,IAAI,CAAC;EAC5E;EACA;EACA,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAuC,IAAI,CAAC;EAC9F,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAM8E,UAAU,GAAG7E,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM8E,kBAAkB,GAAG9E,MAAM,CAAiB,IAAI,CAAC;EACvD,IAAI+E,OAAY;EAChB,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;IAChE,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,EAAEE,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;IAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;IACnC,IAAID,IAAI,YAAYE,WAAW,EAAE;MAChC,OAAOF,IAAI;IACZ,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;MAC/B,OAAOH,IAAI,CAACG,aAAa,CAAC,CAAC;IAC5B,CAAC,MAAM;MACN,OAAO,IAAI;IACZ;EACD,CAAC;EACD,IAAIT,KAAU;EACd,IAAI3C,cAAc,EAAE2C,KAAK,GAAG3C,cAAc,aAAdA,cAAc,wBAAAO,qBAAA,GAAdP,cAAc,CAAEqD,SAAS,cAAA9C,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,uBAA9BA,sBAAA,CAAgC8C,WAAW;EACvE,MAAMC,kBAAkB,GAAIZ,KAAyB,IAAK;IACzD,MAAMa,OAAO,GAAGd,iBAAiB,CAACC,KAAK,IAAI,EAAE,CAAC;IAC9C,IAAIa,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QAAE;QACfC,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAE;MAClB,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACC;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMsG,YAAY,GAAGrG,MAAM,CAAM,IAAI,CAAC;EACxC;EACA,MAAMsG,2BAA2B,GAAGA,CAAA,KAAM;IACzC;IACA,IAAIzB,UAAU,CAAC0B,OAAO,EAAE;MACvB,MAAMC,WAAW,GAAG3B,UAAU,CAAC0B,OAAO,CAACP,qBAAqB,CAAC,CAAC;MAC9D,MAAMS,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,CAACI,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;MACrD,MAAMC,YAAY,GAAGH,IAAI,CAACC,GAAG,CAACH,WAAW,CAACM,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;MAE9D;MACA,MAAMC,YAAY,GAAGC,eAAe,CAAC,CAAC;MACtC,IAAIC,gBAAgB,GAAGR,WAAW;;MAElC;MACA,IAAIM,YAAY,CAACH,KAAK,CAACM,QAAQ,CAAC,MAAM,CAAC,EAAE;QACxC;QACA,MAAMC,aAAa,GAAGJ,YAAY,CAACK,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QACnE,MAAMD,QAAQ,GAAGE,QAAQ,CAACH,aAAa,CAAC,IAAI,GAAG;QAC/CF,gBAAgB,GAAGP,IAAI,CAACa,GAAG,CAACd,WAAW,EAAEW,QAAQ,CAAC;MACnD,CAAC,MAAM;QACN;QACA,MAAMI,eAAe,GAAGT,YAAY,CAACH,KAAK,CAACS,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QAClEJ,gBAAgB,GAAGK,QAAQ,CAACE,eAAe,CAAC,IAAIf,WAAW;MAC5D;MAEA,OAAO;QACNG,KAAK,EAAEK,gBAAgB;QACvBH,MAAM,EAAED;MACT,CAAC;IACF;;IAEA;IACA,MAAME,YAAY,GAAGC,eAAe,CAAC,CAAC;IACtC,IAAIS,cAAc,GAAG,GAAG,CAAC,CAAC;;IAE1B;IACA,IAAIV,YAAY,CAACH,KAAK,CAACM,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxC;MACA,MAAMC,aAAa,GAAGJ,YAAY,CAACK,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;MACnE,MAAMD,QAAQ,GAAGE,QAAQ,CAACH,aAAa,CAAC,IAAI,GAAG;;MAE/C;MACA,IAAIO,iBAAiB,GAAG,GAAG,CAAC,CAAC;;MAE7B;MACA,IAAI7F,mBAAmB,IAAIA,mBAAmB,CAAC8F,MAAM,GAAG,CAAC,EAAE;QAC1D;QACA,MAAMC,eAAe,GAAG/F,mBAAmB,CAACgG,MAAM,CAAC,CAACC,GAAW,EAAEC,KAAU,KAC1ED,GAAG,IAAIC,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/C,MAAMM,aAAa,GAAGL,eAAe,GAAG/F,mBAAmB,CAAC8F,MAAM;QAClE;QACAD,iBAAiB,GAAGhB,IAAI,CAACa,GAAG,CAACb,IAAI,CAACC,GAAG,CAACsB,aAAa,GAAG,GAAG,EAAE,GAAG,CAAC,EAAEb,QAAQ,CAAC;MAC3E;MAEA,IAAItF,eAAe,IAAIA,eAAe,CAAC6F,MAAM,GAAG,CAAC,EAAE;QAClD;QACAD,iBAAiB,GAAGhB,IAAI,CAACC,GAAG,CAACe,iBAAiB,EAAE,GAAG,CAAC;MACrD;MAEA,IAAI3F,YAAY,IAAIA,YAAY,CAAC4F,MAAM,GAAG,CAAC,EAAE;QAC5C;QACA,MAAMO,WAAW,GAAGnG,YAAY,CAAC4F,MAAM,GAAG,EAAE,CAAC,CAAC;QAC9CD,iBAAiB,GAAGhB,IAAI,CAACC,GAAG,CAACe,iBAAiB,EAAEhB,IAAI,CAACa,GAAG,CAACW,WAAW,GAAG,EAAE,EAAEd,QAAQ,CAAC,CAAC;MACtF;MAEAK,cAAc,GAAGf,IAAI,CAACa,GAAG,CAACG,iBAAiB,EAAEN,QAAQ,CAAC;IACvD,CAAC,MAAM;MACN;MACA,MAAMI,eAAe,GAAGT,YAAY,CAACH,KAAK,CAACS,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;MAClEI,cAAc,GAAGH,QAAQ,CAACE,eAAe,CAAC,IAAI,GAAG;IAClD;IAEA,MAAMW,eAAe,GAAG,GAAG,CAAC,CAAC;;IAE7B,OAAO;MACNvB,KAAK,EAAEa,cAAc;MACrBX,MAAM,EAAEqB;IACT,CAAC;EACF,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,WAAoB,IAA0C;IAC5F,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;IAEzC,MAAMC,QAAQ,GAAGN,WAAW,CAACpC,GAAG;IAChC,MAAM2C,WAAW,GAAGH,cAAc,GAAGJ,WAAW,CAACQ,MAAM;IACvD,MAAMC,SAAS,GAAGT,WAAW,CAACnC,IAAI;IAClC,MAAM6C,UAAU,GAAGT,aAAa,GAAGD,WAAW,CAACW,KAAK;IAEpD,MAAMC,QAAQ,GAAGvC,IAAI,CAACC,GAAG,CAACgC,QAAQ,EAAEC,WAAW,EAAEE,SAAS,EAAEC,UAAU,CAAC;IAEvE,IAAIE,QAAQ,KAAKN,QAAQ,EAAE,OAAO,KAAK;IACvC,IAAIM,QAAQ,KAAKL,WAAW,EAAE,OAAO,QAAQ;IAC7C,IAAIK,QAAQ,KAAKH,SAAS,EAAE,OAAO,MAAM;IACzC,OAAO,OAAO;EACf,CAAC;;EAED;EACA,MAAMI,sBAAsB,GAAGA,CAACb,WAAoB,EAAE1D,WAAmB,EAAEwE,OAAe,EAAEC,OAAe,KAAK;IAC/G,MAAMC,WAAW,GAAGhB,WAAW,CAACiB,CAAC,GAAGH,OAAO;IAC3C,MAAMI,UAAU,GAAGlB,WAAW,CAACmB,CAAC,GAAGJ,OAAO;;IAE1C;IACA,MAAMd,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;;IAEzC;IACA,MAAM;MAAE9B,KAAK,EAAE6C,UAAU;MAAE3C,MAAM,EAAE4C;IAAY,CAAC,GAAGpD,2BAA2B,CAAC,CAAC;;IAEhF;IACA,MAAMqD,eAAe,GAAG,EAAE;;IAE1B;IACA,MAAMC,WAAW,GAAG,EAAE;;IAEtB;IACA,MAAMC,iBAAiB,GAAG,CAAC;;IAE3B;IACA,MAAMC,cAAc,GAAGxB,aAAa,GAAIqB,eAAe,GAAG,CAAE;IAC5D,MAAMI,eAAe,GAAGtB,cAAc,GAAIkB,eAAe,GAAG,CAAE;IAC9D,MAAMK,eAAe,GAAGtD,IAAI,CAACa,GAAG,CAACoC,eAAe,EAC/CjD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACa,GAAG,CAAC,CAACuC,cAAc,GAAGL,UAAU,IAAI,CAAC,EAAE,CAACM,eAAe,GAAGL,WAAW,IAAI,CAAC,CAAC,CAC7F,CAAC;;IAED;IACA,MAAMO,YAAY,GAAGZ,WAAW,GAAG1E,WAAW;IAC9C,MAAMuF,aAAa,GAAGX,UAAU,GAAG5E,WAAW;IAC9C,MAAMwF,cAAc,GAAGd,WAAW,GAAI1E,WAAW,GAAG,CAAE;IACtD,MAAMyF,cAAc,GAAGb,UAAU,GAAI5E,WAAW,GAAG,CAAE;;IAErD;IACA,MAAM0F,SAAS,GAAG;IACjB;IACA;MACCC,IAAI,EAAE,eAAe;MACrBpE,IAAI,EAAEiE,cAAc,GAAIV,UAAU,GAAG,CAAE;MACvCxD,GAAG,EAAEiE,aAAa,GAAGN;IACtB,CAAC;IACD;IACA;MACCU,IAAI,EAAE,YAAY;MAClBpE,IAAI,EAAEiE,cAAc,GAAIV,UAAU,GAAG,CAAE;MACvCxD,GAAG,EAAEsD,UAAU,GAAGG,WAAW,GAAGG;IACjC,CAAC;IACD;IACA;MACCS,IAAI,EAAE,cAAc;MACpBpE,IAAI,EAAE+D,YAAY,GAAGL,WAAW;MAChC3D,GAAG,EAAEmE,cAAc,GAAIV,WAAW,GAAG;IACtC,CAAC;IACD;IACA;MACCY,IAAI,EAAE,aAAa;MACnBpE,IAAI,EAAEmD,WAAW,GAAGI,UAAU,GAAGG,WAAW;MAC5C3D,GAAG,EAAEmE,cAAc,GAAIV,WAAW,GAAG;IACtC,CAAC;IACD;IACA;MACCY,IAAI,EAAE,cAAc;MACpBpE,IAAI,EAAE+D,YAAY,GAAGL,WAAW;MAChC3D,GAAG,EAAEiE,aAAa,GAAGN;IACtB,CAAC;IACD;IACA;MACCU,IAAI,EAAE,aAAa;MACnBpE,IAAI,EAAEmD,WAAW,GAAGI,UAAU,GAAGG,WAAW;MAC5C3D,GAAG,EAAEiE,aAAa,GAAGN;IACtB,CAAC,CACD;;IAED;IACA,MAAMW,eAAe,GAAIC,GAAkC,IAAK;MAC/D,MAAMxB,KAAK,GAAGwB,GAAG,CAACtE,IAAI,GAAGuD,UAAU;MACnC,MAAMZ,MAAM,GAAG2B,GAAG,CAACvE,GAAG,GAAGyD,WAAW;MAEpC,OACCc,GAAG,CAACtE,IAAI,IAAI8D,eAAe,IAC3BQ,GAAG,CAACvE,GAAG,IAAI+D,eAAe,IAC1BhB,KAAK,IAAIV,aAAa,GAAG0B,eAAe,IACxCnB,MAAM,IAAIJ,cAAc,GAAGuB,eAAe;IAE5C,CAAC;;IAED;IACA,IAAIS,gBAAgB,GAAGJ,SAAS,CAACK,IAAI,CAACF,GAAG,IAAID,eAAe,CAACC,GAAG,CAAC,CAAC;;IAElE;IACA,IAAI,CAACC,gBAAgB,EAAE;MACtBA,gBAAgB,GAAGJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjC;MACA,IAAII,gBAAgB,CAACvE,IAAI,GAAGuD,UAAU,GAAGnB,aAAa,GAAG0B,eAAe,EAAE;QACzES,gBAAgB,CAACvE,IAAI,GAAGoC,aAAa,GAAGmB,UAAU,GAAGO,eAAe;MACrE;MACA,IAAIS,gBAAgB,CAACvE,IAAI,GAAG8D,eAAe,EAAE;QAC5CS,gBAAgB,CAACvE,IAAI,GAAG8D,eAAe;MACxC;;MAEA;MACA,IAAIS,gBAAgB,CAACxE,GAAG,GAAGyD,WAAW,GAAGjB,cAAc,GAAGuB,eAAe,EAAE;QAC1ES,gBAAgB,CAACxE,GAAG,GAAGwC,cAAc,GAAGiB,WAAW,GAAGM,eAAe;MACtE;MACA,IAAIS,gBAAgB,CAACxE,GAAG,GAAG+D,eAAe,EAAE;QAC3CS,gBAAgB,CAACxE,GAAG,GAAG+D,eAAe;MACvC;IACD;;IAEA;IACA,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC3CC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAChD1C,WAAW,EAAE;UAAEiB,CAAC,EAAEjB,WAAW,CAACiB,CAAC;UAAEE,CAAC,EAAEnB,WAAW,CAACmB,CAAC;UAAE5C,KAAK,EAAEyB,WAAW,CAACzB,KAAK;UAAEE,MAAM,EAAEuB,WAAW,CAACvB;QAAO,CAAC;QACzGkE,eAAe,EAAE;UAAE9E,IAAI,EAAEmD,WAAW;UAAEpD,GAAG,EAAEsD;QAAW,CAAC;QACvD5E,WAAW,EAAEA,WAAW;QACxBsG,OAAO,EAAE;UAAE9B,OAAO;UAAEC;QAAQ,CAAC;QAC7B8B,eAAe,EAAE;UAAEtE,KAAK,EAAE6C,UAAU;UAAE3C,MAAM,EAAE4C;QAAY,CAAC;QAC3DyB,QAAQ,EAAE;UAAEvE,KAAK,EAAE0B,aAAa;UAAExB,MAAM,EAAE2B;QAAe,CAAC;QAC1DgC,gBAAgB,EAAEA,gBAAgB,CAACH,IAAI;QACvCc,aAAa,EAAE;UAAElF,IAAI,EAAEuE,gBAAgB,CAACvE,IAAI;UAAED,GAAG,EAAEwE,gBAAgB,CAACxE;QAAI,CAAC;QACzEoF,YAAY,EAAE;UAAE/B,CAAC,EAAEf,MAAM,CAAC+C,OAAO;UAAE9B,CAAC,EAAEjB,MAAM,CAACgD;QAAQ;MACtD,CAAC,CAAC;IACH;IAEA,OAAO;MACNtF,GAAG,EAAEwE,gBAAgB,CAACxE,GAAG,GAAGsC,MAAM,CAACgD,OAAO;MAC1CrF,IAAI,EAAEuE,gBAAgB,CAACvE,IAAI,GAAGqC,MAAM,CAAC+C;IACtC,CAAC;EACF,CAAC;EACD;EACAxL,SAAS,CAAC,MAAM;IACf,MAAMgG,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;IACxC,IAAIa,OAAO,EAAE;MAAA,IAAA0F,qBAAA,EAAAC,sBAAA;MACZ,MAAM1F,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;;MAE5C;MACA;MACA,MAAM0F,eAAe,GAAGvH,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA8H,qBAAA,GAApB9H,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA+J,qBAAA,eAAvCA,qBAAA,CAAyCG,QAAQ,GAC5GjI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ,IAAAF,sBAAA,GAC9C/H,oBAAoB,CAAC,CAAC,CAAC,cAAA+H,sBAAA,uBAAvBA,sBAAA,CAAyBE,QAAQ;MAEpC,MAAMxC,OAAO,GAAGyC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;MAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;MAC7D,MAAMC,kBAAkB,GAAGH,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,KAAI,IAAI,CAAC;;MAEpE;MACA,MAAMC,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEgG,kBAAkB,EAAE5C,OAAO,EAAEC,OAAO,CAAC;;MAEnF;MACA,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC3CC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UAClDmB,OAAO,EAAE,cAAc;UACvB/H,oBAAoB;UACpB1C,WAAW;UACX4G,WAAW,EAAEtC,IAAI;UACjBoG,YAAY,EAAE;YAAEhD,OAAO;YAAEC,OAAO;YAAEgD,IAAI,EAAEL;UAAmB,CAAC;UAC5DtH,aAAa,EAAEwH;QAChB,CAAC,CAAC;MACH;MAEAvH,gBAAgB,CAACuH,QAAQ,CAAC;IAC3B;EACD,CAAC,EAAE,CAAChH,KAAK,EAAEvB,oBAAoB,EAAES,oBAAoB,EAAE1C,WAAW,CAAC,CAAC;;EAEpE;EACA3B,SAAS,CAAC,MAAM;IAAA,IAAAuM,UAAA;IACf,MAAMvG,OAAO,GAAGd,iBAAiB,CAAC/D,SAAS,aAATA,SAAS,wBAAAoL,UAAA,GAATpL,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAA4K,UAAA,uBAA5BA,UAAA,CAA8BzG,WAAW,CAAC;IAC5EpB,gBAAgB,CAACsB,OAAO,CAAC;IACzB,IAAIA,OAAO,EAAE;MAAA,IAAAwG,sBAAA,EAAAC,sBAAA;MACZzG,OAAO,CAAC0G,KAAK,CAACC,eAAe,GAAG,gBAAgB;;MAEhD;MACA,MAAM1G,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5C;MACA,MAAM0F,eAAe,GAAGvH,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA4I,sBAAA,GAApB5I,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA6K,sBAAA,eAAvCA,sBAAA,CAAyCX,QAAQ,GAC5GjI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ,IAAAY,sBAAA,GAC9C7I,oBAAoB,CAAC,CAAC,CAAC,cAAA6I,sBAAA,uBAAvBA,sBAAA,CAAyBZ,QAAQ;MAEpC,MAAMxC,OAAO,GAAGyC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;MAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;MAC7D,MAAMC,kBAAkB,GAAGH,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,KAAI,IAAI,CAAC;;MAEpE;MACA,MAAMC,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEgG,kBAAkB,EAAE5C,OAAO,EAAEC,OAAO,CAAC;;MAEnF;MACA,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC3CC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACzCmB,OAAO,EAAE,mBAAmB;UAC5B/H,oBAAoB;UACpB1C,WAAW;UACX4G,WAAW,EAAEtC,IAAI;UACjBoG,YAAY,EAAE;YAAEhD,OAAO;YAAEC,OAAO;YAAEgD,IAAI,EAAEL;UAAmB,CAAC;UAC5DtH,aAAa,EAAEwH;QAChB,CAAC,CAAC;MACH;MAEAvH,gBAAgB,CAACuH,QAAQ,CAAC;IAC3B;EACD,CAAC,EAAE,CAAChL,SAAS,EAAEQ,WAAW,EAAEiC,oBAAoB,EAAES,oBAAoB,CAAC,CAAC;;EAExE;EACA;EACA,MAAM,GAAGuI,eAAe,CAAC,GAAG3M,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM4M,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAIlJ,gBAAgB,KAAK,MAAM,EAAE;MAChC,IAAIhC,WAAW,GAAGC,UAAU,EAAE;QAC7B8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;QAC/BF,UAAU,CAAC,CAAC;QACZqL,eAAe,CAACnL,WAAW,GAAGC,UAAU,CAAC;MAC1C;IACD,CAAC,MAAM;MACN8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/B,MAAMoL,eAAe,GAAG1H,QAAQ,CAAC2H,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACL,KAAK,CAACO,OAAO,GAAG,MAAM;QACtCF,eAAe,CAACG,MAAM,CAAC,CAAC;MACzB;IACD;EACD,CAAC;EAED,MAAMJ,eAAe,GAAIK,qBAA8B,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,OAAOV,qBAAqB,gBAC3BrM,OAAA,CAACG,cAAc;MACd2B,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCH,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCzB,QAAQ,EAAEA,QAAS;MACnBsB,cAAc,EAAEA,cAAe;MAC/BrB,SAAS,EAAEA,SAAU;MACrBI,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEsM,cAAe;MAC3BrM,UAAU,EAAEoL,cAAe;MAC3BzL,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAK;MACXC,QAAQ,EAAEA,QAAS;MACnBK,WAAW,EAAEA,WAAW,GAAG,CAAE;MAC7BC,UAAU,EAAEA,UAAW;MACvBC,eAAe,EAAEA,eAAgB;MACjCC,QAAQ,EAAEA,QAAS;MACnBC,mBAAmB,EAAES,cAAc,aAAdA,cAAc,wBAAA4K,sBAAA,GAAd5K,cAAc,CAAEqD,SAAS,cAAAuH,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BzL,WAAW,CAAC,cAAA0L,sBAAA,uBAAxCA,sBAAA,CAA0CU,mBAAoB;MACnF/L,eAAe,EAAEQ,cAAc,aAAdA,cAAc,wBAAA8K,sBAAA,GAAd9K,cAAc,CAAEqD,SAAS,cAAAyH,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B3L,WAAW,CAAC,cAAA4L,sBAAA,uBAAxCA,sBAAA,CAA0CS,eAAgB;MAC3E/L,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAAgL,sBAAA,GAAdhL,cAAc,CAAEqD,SAAS,cAAA2H,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B7L,WAAW,CAAC,cAAA8L,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CQ,aAAa,cAAAP,sBAAA,wBAAAC,uBAAA,GAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,IACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;QAC3C,GAAGA,MAAM;QACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAAZ,uBAAA,uBALDA,uBAAA,CAKG5F,MAAM,CAAC,CAACyG,GAAmB,EAAEC,IAAS,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACvE;MACDvM,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,WAAW,EAAEA,WAAY;MACzBG,YAAY,EAAEA,YAAa;MAC3BE,iBAAiB,EAAE,CAAAD,cAAc,aAAdA,cAAc,wBAAAoL,uBAAA,GAAdpL,cAAc,CAAEqD,SAAS,cAAA+H,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjM,WAAW,GAAG,CAAC,CAAC,cAAAkM,uBAAA,uBAA5CA,uBAAA,CAA8Cc,OAAO,KAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,GACC,IAAI;EACT,CAAC;EAED,MAAMjB,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAInM,WAAW,GAAG,CAAC,EAAE;MACpB+B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/BH,UAAU,CAAC,CAAC;IACb;EACD,CAAC;EACDxB,SAAS,CAAC,MAAM;IACf,IAAIuC,YAAY,EAAE;MACjBqK,eAAe,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACNA,eAAe,CAAC,KAAK,CAAC;IACvB;EACD,CAAC,EAAE,CAACrK,YAAY,CAAC,CAAC;EAClB;EACA,MAAMyM,4BAA4B,GACjCC,QAAgB,IACqD;IACrE,QAAQA,QAAQ;MACf,KAAK,UAAU;QACd,OAAO;UACNC,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UACrDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,WAAW;QACf,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACtDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QACzD,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,YAAY;QAChB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UACvDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF;QACC,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;IACH;EACD,CAAC;EAED,MAAM;IAAEF,YAAY;IAAEG;EAAgB,CAAC,GAAGL,4BAA4B,CAAC,CAAA7M,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmN,QAAQ,KAAI,eAAe,CAAC;EAErH,MAAMC,SAAS,GAAG;IACjBC,UAAU,EAAEzN,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,qBAAA,GAAnBlB,mBAAmB,CAAE0N,cAAc,cAAAxM,qBAAA,eAAnCA,qBAAA,CAAqCyM,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAE5N,mBAAmB,aAAnBA,mBAAmB,gBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAE0N,cAAc,cAAAvM,sBAAA,eAAnCA,sBAAA,CAAqC0M,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAA9N,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAE0N,cAAc,cAAAtM,sBAAA,uBAAnCA,sBAAA,CAAqC2M,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAAhO,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEiO,SAAS,KAAI;EAC9C,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC9C;IACA,OAAO;MACNC,MAAM,EAAED,OAAO,CAAC3I,OAAO,CAAC,qCAAqC,EAAE,CAAC6I,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QACtF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC1C,CAAC;IACF,CAAC;EACF,CAAC;;EAID;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACnC,MAAMC,YAAY,GAAG,KAAK,CAAC,CAAC;IAC5B,MAAMC,YAAY,GAAG,CAAAxO,gBAAgB,aAAhBA,gBAAgB,wBAAAsO,qBAAA,GAAhBtO,gBAAgB,CAAEyO,KAAK,cAAAH,qBAAA,uBAAvBA,qBAAA,CAAyBlJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,KAAImJ,YAAY;IAC/E,OAAOC,YAAY,KAAKD,YAAY;EACrC,CAAC;;EAED;EACA,MAAMxJ,eAAe,GAAGA,CAAA,KAAM;IAC7B;IACA,MAAM2J,cAAc,GAAGL,qBAAqB,CAAC,CAAC;IAE9C,IAAIK,cAAc,IAAI1O,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEyO,KAAK,EAAE;MAC9C;MACA,MAAME,SAAS,GAAG3O,gBAAgB,CAACyO,KAAK,CAACxJ,QAAQ,CAAC,IAAI,CAAC,GACpDjF,gBAAgB,CAACyO,KAAK,GACtB,GAAGzO,gBAAgB,CAACyO,KAAK,IAAI;MAChC,OAAO;QACN9J,KAAK,EAAE,GAAGgK,SAAS,aAAa;QAAE;QAClCxJ,QAAQ,EAAE,GAAGwJ,SAAS,aAAa;QAAE;QACrCC,QAAQ,EAAE,OAAO,CAAC;MACnB,CAAC;IACF,CAAC,MAAM;MACN;MACA;MACA,OAAO;QACNjK,KAAK,EAAE,iBAAiB;QAAE;QAC1BQ,QAAQ,EAAE,kBAAkB;QAAE;QAC9ByJ,QAAQ,EAAE,OAAO,CAAC;MACnB,CAAC;IACF;EACD,CAAC;;EAED;EACA/Q,SAAS,CAAC,MAAM;IACf;IACA,IAAImF,KAAK,IAAIN,WAAW,EAAE;MACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIa,OAAO,EAAE;QAAA,IAAAgL,sBAAA,EAAAC,sBAAA;QACZ,MAAMhL,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;QAC5C;QACA,MAAM0F,eAAe,GAAGvH,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAoN,sBAAA,GAApBpN,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAqP,sBAAA,eAAvCA,sBAAA,CAAyCnF,QAAQ,GAC5GjI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ,IAAAoF,sBAAA,GAC9CrN,oBAAoB,CAAC,CAAC,CAAC,cAAAqN,sBAAA,uBAAvBA,sBAAA,CAAyBpF,QAAQ;QAEpC,MAAMxC,OAAO,GAAGyC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;QAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;QAE7D,MAAMG,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEpB,WAAW,EAAEwE,OAAO,EAAEC,OAAO,CAAC;;QAE5E;QACA,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;YAC7CmB,OAAO,EAAE,gBAAgB;YACzBzH,aAAa,EAAEwH;UAChB,CAAC,CAAC;QACH;QAEAvH,gBAAgB,CAACuH,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAACpK,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,EAAEN,WAAW,EAAEwD,KAAK,EAAEN,WAAW,EAAEjB,oBAAoB,EAAES,oBAAoB,CAAC,CAAC;;EAErI;EACArE,SAAS,CAAC,MAAM;IACf,IAAImF,KAAK,IAAIN,WAAW,EAAE;MACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIa,OAAO,EAAE;QAAA,IAAAkL,sBAAA;QACZ,MAAMjL,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;QAC5C,MAAM0F,eAAe,IAAAsF,sBAAA,GAAGtN,oBAAoB,CAAC,CAAC,CAAC,cAAAsN,sBAAA,uBAAvBA,sBAAA,CAAyBrF,QAAQ;QACzD,MAAMxC,OAAO,GAAGyC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;QAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;QAE7D,MAAMG,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEpB,WAAW,EAAEwE,OAAO,EAAEC,OAAO,CAAC;QAC5E1E,gBAAgB,CAACuH,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAACtH,WAAW,EAAEM,KAAK,EAAEvB,oBAAoB,EAAE7B,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,CAAC,CAAC;;EAElG;EACAjC,SAAS,CAAC,MAAM;IACf,MAAMmR,YAAY,GAAGA,CAAA,KAAM;MAC1B,IAAIhM,KAAK,IAAIN,WAAW,EAAE;QACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIa,OAAO,EAAE;UAAA,IAAAoL,sBAAA;UACZ,MAAMnL,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;UAC5C,MAAM0F,eAAe,IAAAwF,sBAAA,GAAGxN,oBAAoB,CAAC,CAAC,CAAC,cAAAwN,sBAAA,uBAAvBA,sBAAA,CAAyBvF,QAAQ;UACzD,MAAMxC,OAAO,GAAGyC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;UAE7D,MAAMG,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEpB,WAAW,EAAEwE,OAAO,EAAEC,OAAO,CAAC;UAC5E1E,gBAAgB,CAACuH,QAAQ,CAAC;QAC3B;MACD;IACD,CAAC;IAED1D,MAAM,CAAC4I,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAM1I,MAAM,CAAC6I,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EAChE,CAAC,EAAE,CAAChM,KAAK,EAAEN,WAAW,EAAEjB,oBAAoB,CAAC,CAAC;EAE9C,MAAM2N,cAAc,GAAGtP,YAAY,CAAC8F,MAAM,CAAC,CAACyG,GAAQ,EAAEH,MAAW,KAAK;IACrE,MAAMmD,WAAW,GAAGnD,MAAM,CAACC,WAAW,IAAI,SAAS,CAAC,CAAC;IACrD,IAAI,CAACE,GAAG,CAACgD,WAAW,CAAC,EAAE;MACtBhD,GAAG,CAACgD,WAAW,CAAC,GAAG,EAAE;IACtB;IACAhD,GAAG,CAACgD,WAAW,CAAC,CAACC,IAAI,CAACpD,MAAM,CAAC;IAC7B,OAAOG,GAAG;EACX,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMvH,YAAY,GAAGC,eAAe,CAAC,CAAC;EACtC,MAAMwK,WAAW,GAAG;IACnBzC,QAAQ,EAAE,CAAA9M,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmN,QAAQ,KAAI,eAAe;IACvDqC,YAAY,EAAE,CAAAxP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyP,MAAM,KAAI,KAAK;IAC/CC,WAAW,EAAE,CAAA1P,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2P,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAA5P,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6P,WAAW,KAAI,OAAO;IACrDC,WAAW,EAAE,OAAO;IACpBtF,eAAe,EAAE,CAAAxK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE+P,eAAe,KAAI,OAAO;IAC7D;IACAC,OAAO,EAAE,CAAAhQ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiQ,OAAO,KAAI,MAAM;IAC5C;IACA,GAAGnL,YAAY;IACf;IACAoL,SAAS,EAAE,iCAAiC;IAC5C;IACAC,MAAM,EAAEnQ,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE2P,UAAU,IAAI,CAAA3P,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2P,UAAU,MAAK,KAAK,GAC3E,GAAG3P,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2P,UAAU,UAAU,CAAA3P,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6P,WAAW,KAAI,aAAa,EAAE,GACzF;EACJ,CAAC;EACD,MAAMO,aAAa,GAAG,EAAAnP,gBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkCoP,WAAW,cAAAnP,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkEmP,aAAa,KAAI,MAAM;EAC/G,MAAMC,kBAAkB,GAAIC,MAAW,IAAK;IAC3C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAC5F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACAtK,MAAM,CAACuK,QAAQ,CAACC,IAAI,GAAGJ,SAAS;MACjC,CAAC,MAAM;QACN;QACApK,MAAM,CAACyK,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IACCF,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACI,WAAW,IAAI,UAAU,IAChCJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAC/B;QACDjF,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACN6E,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACI,WAAW,IAAI,MAAM,IAC5BJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAC3B;QACDlG,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACN8F,MAAM,CAACC,MAAM,IAAI,SAAS,IAC1BD,MAAM,CAACI,WAAW,IAAI,SAAS,EAC9B;QAAA,IAAAI,uBAAA,EAAAC,uBAAA;QACD;QACA1P,cAAc,CAAC,CAAC,CAAC;QACjB;QACA,IAAIlB,cAAc,aAAdA,cAAc,gBAAA2Q,uBAAA,GAAd3Q,cAAc,CAAEqD,SAAS,cAAAsN,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCtN,WAAW,EAAE;UAChD,MAAMuN,gBAAgB,GAAGnO,iBAAiB,CAAC1C,cAAc,CAACqD,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UACnF,IAAIuN,gBAAgB,EAAE;YACrBA,gBAAgB,CAACC,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACxD;QACD;MACD;IACD;IACA3G,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD5M,SAAS,CAAC,MAAM;IAAA,IAAAwT,WAAA,EAAAC,mBAAA;IACf,IAAItS,SAAS,aAATA,SAAS,gBAAAqS,WAAA,GAATrS,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAA6R,WAAA,gBAAAC,mBAAA,GAA5BD,WAAA,CAA8B7E,OAAO,cAAA8E,mBAAA,eAArCA,mBAAA,CAAuCC,aAAa,EAAE;MACzD;MACAzP,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,EAAEsC,cAAc,CAAC,CAAC;;EAE/D;EACAjE,SAAS,CAAC,MAAM;IACf,IAAI4C,kBAAkB,EAAE;MAAA,IAAA+Q,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAMpI,eAAe,GAAGvH,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA+P,sBAAA,GAApB/P,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAgS,sBAAA,eAAvCA,sBAAA,CAAyC9H,QAAQ,GAC5GjI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ,IAAA+H,uBAAA,GAC9ChQ,oBAAoB,CAAC,CAAC,CAAC,cAAAgQ,uBAAA,uBAAvBA,uBAAA,CAAyB/H,QAAQ;MACpC,MAAMoI,WAAW,GAAG5P,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAqR,uBAAA,GAAdrR,cAAc,CAAEqD,SAAS,cAAAgO,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BlS,WAAW,GAAG,CAAC,CAAC,cAAAmS,uBAAA,uBAA5CA,uBAAA,CAA8CnF,OAAO,GACrDnM,cAAc,aAAdA,cAAc,wBAAAuR,uBAAA,GAAdvR,cAAc,CAAEqD,SAAS,cAAAkO,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCrF,OAAO;;MAE1C;MACA;MACA,IAAI/C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE8H,aAAa,EAAE;QACnC;QACAzP,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACrB,kBAAkB,EAAEgB,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,IAAI6C,kBAAkB,EAAE;MAAA,IAAAqR,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAM3I,eAAe,GAAGvH,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAsQ,uBAAA,GAApBtQ,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAuS,uBAAA,eAAvCA,uBAAA,CAAyCrI,QAAQ,GAC5GjI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ,IAAAsI,uBAAA,GAC9CvQ,oBAAoB,CAAC,CAAC,CAAC,cAAAuQ,uBAAA,uBAAvBA,uBAAA,CAAyBtI,QAAQ;MACpC,MAAMoI,WAAW,GAAG5P,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAA4R,uBAAA,GAAd5R,cAAc,CAAEqD,SAAS,cAAAuO,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BzS,WAAW,GAAG,CAAC,CAAC,cAAA0S,uBAAA,uBAA5CA,uBAAA,CAA8C1F,OAAO,GACrDnM,cAAc,aAAdA,cAAc,wBAAA8R,uBAAA,GAAd9R,cAAc,CAAEqD,SAAS,cAAAyO,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgC5F,OAAO;;MAE1C;MACA,IAAI/C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE8H,aAAa,EAAE;QACnC;QACAzP,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACpB,kBAAkB,EAAEe,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,MAAMwU,iBAAiB,GAAIC,CAAa,IAAK;MAC5C,MAAMC,cAAc,GAAGrP,QAAQ,CAAC2H,cAAc,CAAC,cAAc,CAAC;;MAE9D;MACA,IAAI0H,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,EAAE;QAChE;MACD;;MAEA;MACA;IACD,CAAC;IAEDvP,QAAQ,CAACgM,gBAAgB,CAAC,OAAO,EAAEmD,iBAAiB,CAAC;IAErD,OAAO,MAAM;MACZnP,QAAQ,CAACiM,mBAAmB,CAAC,OAAO,EAAEkD,iBAAiB,CAAC;IACzD,CAAC;EACF,CAAC,EAAE,CAAC5Q,oBAAoB,CAAC,CAAC;EAC1B;EACA5D,SAAS,CAAC,MAAM;IACf,MAAM6U,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAI9P,UAAU,CAAC0B,OAAO,EAAE;QACvB;QACA1B,UAAU,CAAC0B,OAAO,CAACiG,KAAK,CAAC1F,MAAM,GAAG,MAAM;QACxC,MAAM8N,aAAa,GAAG/P,UAAU,CAAC0B,OAAO,CAACsO,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpD1O,iBAAiB,CAAC2O,YAAY,CAAC;;QAE/B;QACA,IAAI1O,YAAY,CAACE,OAAO,EAAE;UACzB;UACA,IAAIF,YAAY,CAACE,OAAO,CAACyO,YAAY,EAAE;YACtC3O,YAAY,CAACE,OAAO,CAACyO,YAAY,CAAC,CAAC;UACpC;UACA;UACAC,UAAU,CAAC,MAAM;YAChB,IAAI5O,YAAY,CAACE,OAAO,IAAIF,YAAY,CAACE,OAAO,CAACyO,YAAY,EAAE;cAC9D3O,YAAY,CAACE,OAAO,CAACyO,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDL,iBAAiB,CAAC,CAAC;IAGnB,MAAMO,QAAQ,GAAG,CAChBD,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC,EACjCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,EAClCM,UAAU,CAACN,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIQ,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAIvQ,UAAU,CAAC0B,OAAO,IAAIgC,MAAM,CAAC8M,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCJ,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFQ,cAAc,CAACG,OAAO,CAACzQ,UAAU,CAAC0B,OAAO,CAAC;IAC3C;IAGA,IAAI1B,UAAU,CAAC0B,OAAO,IAAIgC,MAAM,CAACgN,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CN,UAAU,CAACN,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,gBAAgB,CAACE,OAAO,CAACzQ,UAAU,CAAC0B,OAAO,EAAE;QAC5CiP,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC;MAC9B,IAAIV,cAAc,EAAE;QACnBA,cAAc,CAACW,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIV,gBAAgB,EAAE;QACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAACrU,WAAW,CAAC,CAAC;EACjB;EACA;;EAEA,SAASsU,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EACA,MAAMC,iBAAiB,GAAGA,CAAClH,QAAgB,GAAG,eAAe,KAAK;IACjE,QAAQA,QAAQ;MACf,KAAK,aAAa;QACjB,OAAO;UAAE9I,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,cAAc;QAClB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,aAAa;QACjB,OAAO;UAAEA,GAAG,EAAE7E,QAAQ,KAAK,EAAE,GAAG,gBAAgB,GAAG;QAAiB,CAAC;MACtE,KAAK,cAAc;QAClB,OAAO;UAAE6E,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,UAAU;QACd,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,WAAW;QACf,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,YAAY;QAChB,OAAO;UAAEA,GAAG,EAAE;QAAgB,CAAC;MAChC;QACC,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;IAClC;EACD,CAAC;;EAEA;EACD,MAAMiQ,kBAAkB,GAAGA,CAACC,QAAgB,EAAEzK,eAAoB,EAAEqI,WAAgB,KAAK;IACxF,IAAI5P,oBAAoB,KAAK,SAAS,EAAE;MACvC;MACA,QAAQgS,QAAQ;QACf,KAAK,gBAAgB;UACpB,OAAO,CAAApC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqC,cAAc,MAAKC,SAAS,GAAGtC,WAAW,CAACqC,cAAc,GAAG1K,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0K,cAAc;QAChH,KAAK,eAAe;UACnB;UACA,OAAO,CAAArC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuC,4BAA4B,MAAKD,SAAS,GAAGtC,WAAW,CAACuC,4BAA4B,GAAG5K,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4K,4BAA4B;QAC1J,KAAK,UAAU;UACd,OAAO,CAAAvC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwC,QAAQ,MAAKF,SAAS,GAAGtC,WAAW,CAACwC,QAAQ,GAAG7K,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6K,QAAQ;QAC9F,KAAK,eAAe;UACnB,OAAO,CAAAxC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,aAAa,MAAK6C,SAAS,GAAGtC,WAAW,CAACP,aAAa,GAAG9H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8H,aAAa;QAC7G;UACC,OAAO9H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGyK,QAAQ,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACA,IAAIA,QAAQ,KAAK,eAAe,EAAE;QACjC,OAAOzK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4K,4BAA4B;MACrD;MACA,OAAO5K,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGyK,QAAQ,CAAC;IACnC;EACD,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAACzR,OAAY,EAAE2G,eAAoB,EAAEqI,WAAgB,EAAE7N,IAAS,EAAED,GAAQ,KAAK;IACzGlB,OAAO,CAACyH,KAAK,CAACuC,QAAQ,GAAG,UAAU;IACnChK,OAAO,CAACyH,KAAK,CAACtG,IAAI,GAAG,GAAGA,IAAI,IAAI;IAChCnB,OAAO,CAACyH,KAAK,CAACvG,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC9BlB,OAAO,CAACyH,KAAK,CAAC5F,KAAK,GAAG,GAAG8E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,IAAI,CAAC,CAAC;IACpDjH,OAAO,CAACyH,KAAK,CAAC1F,MAAM,GAAG,GAAG4E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,IAAI,IAAI;IACnDjH,OAAO,CAACyH,KAAK,CAACC,eAAe,GAAGf,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+K,KAAK;IACtD1R,OAAO,CAACyH,KAAK,CAACiF,YAAY,GAAG,KAAK;IAClC1M,OAAO,CAACyH,KAAK,CAACkK,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC1C3R,OAAO,CAACyH,KAAK,CAACmK,UAAU,GAAG,MAAM;IACjC5R,OAAO,CAACyH,KAAK,CAACoK,aAAa,GAAG,MAAM,CAAC,CAAC;IACtC7R,OAAO,CAAC8R,SAAS,GAAG,EAAE;IAEtB,IAAI,CAAAnL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoL,IAAI,MAAK,MAAM,IAAI,CAAApL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoL,IAAI,MAAK,UAAU,EAAE;MAC7E,MAAMC,QAAQ,GAAG5R,QAAQ,CAAC6R,aAAa,CAAC,MAAM,CAAC;MAC/CD,QAAQ,CAACE,SAAS,GAAGvL,eAAe,CAACoL,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MAChEC,QAAQ,CAACvK,KAAK,CAACmD,KAAK,GAAG,OAAO;MAC9BoH,QAAQ,CAACvK,KAAK,CAAC0K,QAAQ,GAAG,MAAM;MAChCH,QAAQ,CAACvK,KAAK,CAAC8C,UAAU,GAAG,MAAM;MAClCyH,QAAQ,CAACvK,KAAK,CAACiD,SAAS,GAAG/D,eAAe,CAACoL,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;MAChFC,QAAQ,CAACvK,KAAK,CAACO,OAAO,GAAG,MAAM;MAC/BgK,QAAQ,CAACvK,KAAK,CAAC2K,UAAU,GAAG,QAAQ;MACpCJ,QAAQ,CAACvK,KAAK,CAAC4K,cAAc,GAAG,QAAQ;MACxCL,QAAQ,CAACvK,KAAK,CAAC5F,KAAK,GAAG,MAAM;MAC7BmQ,QAAQ,CAACvK,KAAK,CAAC1F,MAAM,GAAG,MAAM;MAC9B/B,OAAO,CAACsS,WAAW,CAACN,QAAQ,CAAC;IAC9B;;IAEA;IACA;IACA,MAAMO,qBAAqB,GAAGpB,kBAAkB,CAAC,gBAAgB,EAAExK,eAAe,EAAEqI,WAAW,CAAC;IAChG,MAAMwD,WAAW,GAAGpT,oBAAoB,KAAK,SAAS,GAClDmT,qBAAqB,KAAK,KAAK,IAAI,CAACvS,OAAO,CAACyS,aAAa,GACzD9L,eAAe,IAAIzH,gBAAgB,IAAI,CAACc,OAAO,CAACyS,aAAc;IAElE,IAAID,WAAW,EAAE;MACPxS,OAAO,CAAC0S,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxC3S,OAAO,CAAC0S,SAAS,CAACzK,MAAM,CAAC,yBAAyB,CAAC;IACvD,CAAC,MAAM;MACHjI,OAAO,CAAC0S,SAAS,CAACzK,MAAM,CAAC,iBAAiB,CAAC;MAC3CjI,OAAO,CAAC0S,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACpD;;IAEN;IACA3S,OAAO,CAACyH,KAAK,CAACO,OAAO,GAAG,MAAM;IAC9BhI,OAAO,CAACyH,KAAK,CAACoK,aAAa,GAAG,MAAM;;IAEpC;IACA;IACA;IACA,MAAMe,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAExK,eAAe,EAAEqI,WAAW,CAAC;IACvF,IAAI4D,aAAa,EAAE;MAClB5T,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACA;IAAA;;IAGD;IACA;IACA,IAAI,CAACgB,OAAO,CAAC6S,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,MAAMC,UAAU,GAAG9S,OAAO,CAAC+S,SAAS,CAAC,IAAI,CAAgB;MACzD;MACA,IAAI/S,OAAO,CAACyS,aAAa,KAAKnB,SAAS,EAAE;QAC9BwB,UAAU,CAASL,aAAa,GAAGzS,OAAO,CAACyS,aAAa;MAC7D;MACN,IAAIzS,OAAO,CAACgT,UAAU,EAAE;QACvBhT,OAAO,CAACgT,UAAU,CAACC,YAAY,CAACH,UAAU,EAAE9S,OAAO,CAAC;QACpDA,OAAO,GAAG8S,UAAU;MACrB;IACD;;IAEA;IACA9S,OAAO,CAACyH,KAAK,CAACoK,aAAa,GAAG,MAAM;;IAEpC;IACA,MAAMqB,QAAQ,GAAG/B,kBAAkB,CAAC,UAAU,EAAExK,eAAe,EAAEqI,WAAW,CAAC;IAC7E,MAAMmE,WAAW,GAAI3D,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnBrN,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIkN,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACAlU,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACA,MAAM4V,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAExK,eAAe,EAAEqI,WAAW,CAAC;QAC9F,IAAIqE,oBAAoB,EAAE;UACzBrT,OAAO,CAAC0S,SAAS,CAACzK,MAAM,CAAC,iBAAiB,CAAC;UAC3CjI,OAAO,CAAC0S,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD3S,OAAO,CAACyS,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;IAED,MAAMa,cAAc,GAAI9D,CAAQ,IAAK;MACpCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;;MAEnB;MACA;MACA,MAAMR,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAExK,eAAe,EAAEqI,WAAW,CAAC;MACvF,IAAIkE,QAAQ,KAAK,kBAAkB,IAAI,CAACN,aAAa,EAAE;QACtD;MAAA;IAEF,CAAC;IAED,MAAMW,WAAW,GAAI/D,CAAQ,IAAK;MACjCA,CAAC,CAAC4D,eAAe,CAAC,CAAC;MACnBrN,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIkN,QAAQ,KAAK,kBAAkB,IAAI,CAACA,QAAQ,EAAE;QACjD;QACAlU,cAAc,CAAC,CAACC,WAAW,CAAC;;QAE5B;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACJ,MAAM2V,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAExK,eAAe,EAAEqI,WAAW,CAAC;QAC1F,IAAIqE,oBAAoB,EAAE;UACzBrT,OAAO,CAAC0S,SAAS,CAACzK,MAAM,CAAC,iBAAiB,CAAC;UAC3CjI,OAAO,CAAC0S,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChD3S,OAAO,CAACyS,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;;IAED;IACA,IAAI,CAACzS,OAAO,CAAC6S,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,IAAIK,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACAlT,OAAO,CAACoM,gBAAgB,CAAC,WAAW,EAAE+G,WAAW,CAAC;QAClDnT,OAAO,CAACoM,gBAAgB,CAAC,UAAU,EAAEkH,cAAc,CAAC;;QAEpD;QACAtT,OAAO,CAACoM,gBAAgB,CAAC,OAAO,EAAEmH,WAAW,CAAC;MAC/C,CAAC,MAAM;QACN;QACAvT,OAAO,CAACoM,gBAAgB,CAAC,OAAO,EAAEmH,WAAW,CAAC;MAC/C;;MAEA;MACAvT,OAAO,CAACwT,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;IACxD;EACD,CAAC;EACDzY,SAAS,CAAC,MAAM;IACf,IAAIgG,OAAO;IACX,IAAI0S,KAAK;IAET,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,MAAA,EAAAC,OAAA;QACH;QACAL,KAAK,GAAG,CAAAlW,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqD,SAAS,KAAI,EAAE;;QAEvC;QACA,MAAMmT,WAAW,GAAG3U,oBAAoB,KAAK,SAAS,IAAI7B,cAAc,aAAdA,cAAc,gBAAAoW,uBAAA,GAAdpW,cAAc,CAAEqD,SAAS,cAAA+S,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjX,WAAW,GAAG,CAAC,CAAC,cAAAkX,uBAAA,eAA5CA,uBAAA,CAA8C/S,WAAW,GAC/GtD,cAAc,CAACqD,SAAS,CAAClE,WAAW,GAAG,CAAC,CAAC,CAASmE,WAAW,GAC9D,EAAAgT,MAAA,GAAAJ,KAAK,cAAAI,MAAA,wBAAAC,OAAA,GAALD,MAAA,CAAQ,CAAC,CAAC,cAAAC,OAAA,uBAAVA,OAAA,CAAYjT,WAAW,KAAI,EAAE;QAEhCE,OAAO,GAAGd,iBAAiB,CAAC8T,WAAW,IAAI,EAAE,CAAC;QAC9CtU,gBAAgB,CAACsB,OAAO,CAAC;QAEzB,IAAIA,OAAO,EAAE;UACZ;QAAA;;QAGD;QACA,MAAMiT,iBAAiB,GAAGtV,gBAAgB,KAAK,SAAS,IACvDU,oBAAoB,KAAK,SAAS,IAClCjD,KAAK,KAAK,SAAS,IAClBuC,gBAAgB,KAAK,MAAM,IAAIU,oBAAoB,KAAK,SAAU;QAEpE,IAAI4U,iBAAiB,EAAE;UAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAItB;UACA,IAAI3N,eAAe;UACnB,IAAIqI,WAAW;UAEf,IAAI5P,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAsV,uBAAA,GAApBtV,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAuX,uBAAA,eAAvCA,uBAAA,CAAyCrN,QAAQ,EAAE;YAAA,IAAA2N,uBAAA,EAAAC,uBAAA;YAC5F;YACA7N,eAAe,GAAGhI,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACkK,QAAQ;YAChEoI,WAAW,GAAGzR,cAAc,aAAdA,cAAc,wBAAAgX,uBAAA,GAAdhX,cAAc,CAAEqD,SAAS,cAAA2T,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B7X,WAAW,GAAG,CAAC,CAAC,cAAA8X,uBAAA,uBAA5CA,uBAAA,CAA8C9K,OAAO;UACpE,CAAC,MAAM,IAAI/K,oBAAoB,aAApBA,oBAAoB,gBAAAuV,uBAAA,GAApBvV,oBAAoB,CAAG,CAAC,CAAC,cAAAuV,uBAAA,eAAzBA,uBAAA,CAA2BtN,QAAQ,EAAE;YAAA,IAAA6N,uBAAA,EAAAC,uBAAA;YAC/C;YACA/N,eAAe,GAAGhI,oBAAoB,CAAC,CAAC,CAAC,CAACiI,QAAQ;YAClDoI,WAAW,GAAGzR,cAAc,aAAdA,cAAc,wBAAAkX,uBAAA,GAAdlX,cAAc,CAAEqD,SAAS,cAAA6T,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgChL,OAAO;UACtD,CAAC,MAAM;YAAA,IAAAiL,uBAAA,EAAAC,uBAAA;YACN;YACAjO,eAAe,GAAG;cACjBG,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACdgL,IAAI,EAAE,UAAU;cAChBL,KAAK,EAAE,QAAQ;cACfzK,IAAI,EAAE,IAAI;cACVoK,cAAc,EAAE,IAAI;cACpBE,4BAA4B,EAAE,IAAI;cAClCC,QAAQ,EAAE,kBAAkB;cAC5B/C,aAAa,EAAE;YAChB,CAAC;YACDO,WAAW,GAAG,CAAAzR,cAAc,aAAdA,cAAc,wBAAAoX,uBAAA,GAAdpX,cAAc,CAAEqD,SAAS,cAAA+T,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjY,WAAW,GAAG,CAAC,CAAC,cAAAkY,uBAAA,uBAA5CA,uBAAA,CAA8ClL,OAAO,KAAI,CAAC,CAAC;UAC1E;UACA,MAAMtF,OAAO,GAAGyC,UAAU,CAAC,EAAAsN,gBAAA,GAAAxN,eAAe,cAAAwN,gBAAA,uBAAfA,gBAAA,CAAiBrN,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMzC,OAAO,GAAGwC,UAAU,CAAC,EAAAuN,iBAAA,GAAAzN,eAAe,cAAAyN,iBAAA,uBAAfA,iBAAA,CAAiBrN,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMC,kBAAkB,GAAGH,UAAU,CAAC,EAAAwN,iBAAA,GAAA1N,eAAe,cAAA0N,iBAAA,uBAAfA,iBAAA,CAAiBpN,IAAI,KAAI,IAAI,CAAC;;UAEpE;UACApH,cAAc,CAACmH,kBAAkB,CAAC;UAElC,IAAI7F,IAAI,EAAED,GAAG;UACb,IAAIH,OAAO,EAAE;YACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;YAC5CE,IAAI,GAAGH,IAAI,CAACuD,CAAC,GAAGH,OAAO;YACvBlD,GAAG,GAAGF,IAAI,CAACyD,CAAC,IAAIJ,OAAO,GAAG,CAAC,GAAG,CAACA,OAAO,GAAG1C,IAAI,CAACkT,GAAG,CAACxQ,OAAO,CAAC,CAAC;;YAE3D;YACA,MAAM6C,QAAQ,GAAG/C,sBAAsB,CAACnD,IAAI,EAAEgG,kBAAkB,EAAE5C,OAAO,EAAEC,OAAO,CAAC;YACnF1E,gBAAgB,CAACuH,QAAQ,CAAC;UAC3B;;UAEA;UACA,MAAMY,eAAe,GAAG1H,QAAQ,CAAC2H,cAAc,CAAC,cAAc,CAAC;UAC/D,IAAID,eAAe,EAAE;YACpB9H,OAAO,GAAG8H,eAAe;YACzB;UACD,CAAC,MAAM;YACN;YACA9H,OAAO,GAAGI,QAAQ,CAAC6R,aAAa,CAAC,KAAK,CAAC;YACvCjS,OAAO,CAAC8U,EAAE,GAAG,cAAc,CAAC,CAAC;YAC7B9U,OAAO,CAACyS,aAAa,GAAG,KAAK,CAAC,CAAC;YAC/BrS,QAAQ,CAAC2U,IAAI,CAACzC,WAAW,CAACtS,OAAO,CAAC;UACnC;UAEAA,OAAO,CAACyH,KAAK,CAACuN,MAAM,GAAG,SAAS;UAChChV,OAAO,CAACyH,KAAK,CAACoK,aAAa,GAAG,MAAM,CAAC,CAAC;;UAEtC;UACA7R,OAAO,CAACyH,KAAK,CAACkK,MAAM,GAAG,MAAM;;UAE7B;UACA,KAAA2C,iBAAA,GAAI3N,eAAe,cAAA2N,iBAAA,eAAfA,iBAAA,CAAiB7F,aAAa,EAAE;YACnCzP,cAAc,CAAC,IAAI,CAAC;UACrB;;UAEA;UACAyS,kBAAkB,CAACzR,OAAO,EAAE2G,eAAe,EAAEqI,WAAW,EAAE7N,IAAI,EAAED,GAAG,CAAC;;UAEpE;UACA,MAAM0R,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAExK,eAAe,EAAEqI,WAAW,CAAC;UACvF,IAAI4D,aAAa,EAAE;YAClB5T,cAAc,CAAC,IAAI,CAAC;UACrB,CAAC,MAAM;YACN;UAAA;;UAGD;QACD;MACD,CAAC,CAAC,OAAOiW,KAAK,EAAE;QACflP,OAAO,CAACkP,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACD,CAAC;IAEDvB,iBAAiB,CAAC,CAAC;IAEnB,OAAO,MAAM;MACZ,MAAM5L,eAAe,GAAG1H,QAAQ,CAAC2H,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACoN,OAAO,GAAG,IAAI;QAC9BpN,eAAe,CAACqN,WAAW,GAAG,IAAI;QAClCrN,eAAe,CAACsN,UAAU,GAAG,IAAI;MAClC;IACD,CAAC;EACF,CAAC,EAAE,CACF7X,cAAc,EACdoB,oBAAoB,EACpBhB,kBAAkB,EAClBC,kBAAkB,EAClBwB,oBAAoB,EACpB1C;EACA;EAAA,CACA,CAAC;EACF,MAAM2Y,cAAc,GAAG,CAAA9X,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAEqD,SAAS,cAAAtC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgC+W,OAAO,cAAA9W,uBAAA,uBAAvCA,uBAAA,CAAyC+W,cAAc,KAAI,KAAK;EAEvF,SAASC,mBAAmBA,CAACnW,cAAmB,EAAE;IAAA,IAAAoW,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACjD,IAAItW,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA9B,cAAc,aAAdA,cAAc,wBAAAkY,uBAAA,GAAdlY,cAAc,CAAEqD,SAAS,cAAA6U,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCJ,OAAO,cAAAK,uBAAA,uBAAvCA,uBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAACnW,cAAc,CAAC;EAC5D,MAAMyW,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIQ,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCha,OAAA,CAACP,aAAa;QACbya,OAAO,EAAC,MAAM;QACdtC,KAAK,EAAE9W,UAAW;QAClBqN,QAAQ,EAAC,QAAQ;QACjBgM,UAAU,EAAEtZ,WAAW,GAAG,CAAE;QAC5BuZ,EAAE,EAAE;UACHvO,eAAe,EAAE,aAAa;UAC9BsC,QAAQ,EAAE,oBAAoB;UAC9B,+BAA+B,EAAE;YAChCtC,eAAe,EAAEpI,aAAa,CAAE;UACjC;QACD,CAAE;QACF4W,UAAU,eAAEra,OAAA,CAACV,MAAM;UAACsM,KAAK,EAAE;YAAE0O,UAAU,EAAE;UAAS;QAAE;UAAAxM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDsM,UAAU,eAAEva,OAAA,CAACV,MAAM;UAACsM,KAAK,EAAE;YAAE0O,UAAU,EAAE;UAAS;QAAE;UAAAxM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACA,IAAI+L,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCha,OAAA,CAACX,GAAG;QAAC+a,EAAE,EAAE;UAAEjO,OAAO,EAAE,MAAM;UAAEoK,UAAU,EAAE,QAAQ;UAAEiE,YAAY,EAAE,QAAQ;UAAEC,GAAG,EAAE,KAAK;UAAEpJ,OAAO,EAAE;QAAM,CAAE;QAAAqJ,QAAA,EAGrGC,KAAK,CAACC,IAAI,CAAC;UAAE7T,MAAM,EAAEjG;QAAW,CAAC,CAAC,CAACsM,GAAG,CAAC,CAACyN,CAAC,EAAEC,KAAK,kBAChD9a,OAAA;UAEC4L,KAAK,EAAE;YACN5F,KAAK,EAAE,MAAM;YACbE,MAAM,EAAE,KAAK;YACb2F,eAAe,EAAEiP,KAAK,KAAKja,WAAW,GAAG,CAAC,GAAG4C,aAAa,GAAG,SAAS;YAAE;YACxEoN,YAAY,EAAE;UACf;QAAE,GANGiK,KAAK;UAAAhN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;IACA,IAAI+L,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCha,OAAA,CAACX,GAAG;QAAC+a,EAAE,EAAE;UAAEjO,OAAO,EAAE,MAAM;UAAEoK,UAAU,EAAE,QAAQ;UAAEiE,YAAY,EAAE;QAAa,CAAE;QAAAE,QAAA,eAC9E1a,OAAA,CAACL,UAAU;UAACya,EAAE,EAAE;YAAE/I,OAAO,EAAE,KAAK;YAAEtC,KAAK,EAAEtL;UAAc,CAAE;UAAAiX,QAAA,GAAC,OACpD,EAAC7Z,WAAW,EAAC,MAAI,EAACC,UAAU;QAAA;UAAAgN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAI+L,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCha,OAAA,CAACX,GAAG;QAAAqb,QAAA,eACH1a,OAAA,CAACL,UAAU;UAACua,OAAO,EAAC,OAAO;UAAAQ,QAAA,eAC1B1a,OAAA,CAACR,cAAc;YACd0a,OAAO,EAAC,aAAa;YACrBa,KAAK,EAAE/Z,QAAS;YAChBoZ,EAAE,EAAE;cACHlU,MAAM,EAAE,KAAK;cACX2K,YAAY,EAAE,MAAM;cACpBmK,MAAM,EAAE,UAAU;cACpB,0BAA0B,EAAE;gBAC3BnP,eAAe,EAAEpI,aAAa,CAAE;cACjC;YACD;UAAE;YAAAqK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACCjO,OAAA,CAAAE,SAAA;IAAAwa,QAAA,GACE/W,aAAa,iBACb3D,OAAA;MAAA0a,QAAA,EAcEtX,WAAW,iBACXpD,OAAA,CAACN,OAAO;QACP0S,IAAI,EAAE6I,OAAO,CAACpX,aAAa,CAAC,IAAIoX,OAAO,CAAC7a,QAAQ,CAAE;QAClDA,QAAQ,EAAEA,QAAS;QACnBK,OAAO,EAAEA,CAAA,KAAM;UACd;UACA;QAAA,CACC;QACF2N,YAAY,EAAEA,YAAa;QAC3BG,eAAe,EAAEA,eAAgB;QACjC2M,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EACbtX,aAAa,GACV;UACAwB,GAAG,EAAExB,aAAa,CAACwB,GAAG,IAAI2F,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC8H,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,GAAG4C,IAAI,CAACkT,GAAG,CAAChO,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC;UAC7IoC,IAAI,EAAEzB,aAAa,CAACyB,IAAI,GAAG0F,UAAU,CAAC/H,YAAY,IAAI,GAAG;QACzD,CAAC,GACDwS,SACH;QACD2E,EAAE,EAAE;UACH;UACA;UACA;UACA,gBAAgB,EAAEha,QAAQ,GAAG,MAAM,GAAG,MAAM;UAC5C,8CAA8C,EAAE;YAC/C0V,MAAM,EAAE,IAAI;YACZ;YACA,GAAGlF,WAAW;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,GAAGyE,iBAAiB,CAAC,CAAAhU,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmN,QAAQ,KAAI,eAAe,CAAC;YACnEnJ,GAAG,EAAE,GAAG,CAAC,CAAAxB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwB,GAAG,KAAI,CAAC,KAC5BnC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC3C8H,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAClC,CAAC8H,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,GAChC4C,IAAI,CAACkT,GAAG,CAAChO,UAAU,CAAC9H,YAAY,IAAI,GAAG,CAAC,CAAC,GAC1C,CAAC,CAAC,eAAe;YACrBoC,IAAI,EAAE,GAAG,CAAC,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyB,IAAI,KAAI,CAAC,KAAKrC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC9E+H,UAAU,CAAC/H,YAAY,CAAC,IAAI,CAAC,GAC9B,CAAC,CAAE,eAAe;YACrBmY,QAAQ,EAAE,QAAQ;YAClB;YACArF,UAAU,EAAE;UACb;QACD,CAAE;QACFsF,iBAAiB,EAAE,IAAK;QAAAX,QAAA,gBAExB1a,OAAA;UAAK4L,KAAK,EAAE;YAAE4O,YAAY,EAAE,KAAK;YAAErO,OAAO,EAAE;UAAO,CAAE;UAAAuO,QAAA,EACnD,CAAAtZ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEka,aAAa,kBAC9Btb,OAAA,CAACT,UAAU;YACVgc,OAAO,EAAEA,CAAA,KAAM;cACd;cACA;YAAA,CACC;YACFnB,EAAE,EAAE;cACHjM,QAAQ,EAAE,OAAO;cACjBoD,SAAS,EAAE,iCAAiC;cAC5CjM,IAAI,EAAE,MAAM;cACZ8C,KAAK,EAAE,MAAM;cACb4S,MAAM,EAAE,OAAO;cACfQ,UAAU,EAAE,iBAAiB;cAC7BhK,MAAM,EAAE,gBAAgB;cACxBsE,MAAM,EAAE,QAAQ;cAChBjF,YAAY,EAAE,MAAM;cACpBQ,OAAO,EAAE;YACV,CAAE;YAAAqJ,QAAA,eAEF1a,OAAA,CAACJ,SAAS;cAACwa,EAAE,EAAE;gBAAEqB,IAAI,EAAE,CAAC;gBAAE1M,KAAK,EAAE;cAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACNjO,OAAA,CAACF,gBAAgB;UAEpB4b,GAAG,EAAEjW,YAAa;UAClBmG,KAAK,EAAE;YAAE+P,SAAS,EAAE;UAAQ,CAAE;UAC9BC,OAAO,EAAE;YACRC,eAAe,EAAE,CAACtW,cAAc;YAChCuW,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAAzB,QAAA,eAEC1a,OAAA;YAAK4L,KAAK,EAAE;cACX+P,SAAS,EAAE,OAAO;cAClBP,QAAQ,EAAE;YACX,CAAE;YAAAV,QAAA,eACD1a,OAAA,CAACX,GAAG;cAACuM,KAAK,EAAE;gBACX;gBACA1F,MAAM,EAAEuL;cACT,CAAE;cAAAiJ,QAAA,gBACD1a,OAAA,CAACX,GAAG;gBACHqc,GAAG,EAAEzX,UAAW;gBAChBkI,OAAO,EAAC,MAAM;gBACdiQ,aAAa,EAAC,QAAQ;gBACtBC,QAAQ,EAAC,MAAM;gBACf7F,cAAc,EAAC,QAAQ;gBACvB4D,EAAE,EAAE;kBACHpU,KAAK,EAAE,MAAM;kBACbqL,OAAO,EAAE,KAAK;kBACdiL,SAAS,EAAE;gBACZ,CAAE;gBAAA5B,QAAA,GAEDxZ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkM,GAAG,CAAEmP,SAAc,IACpCA,SAAS,CAAC7K,WAAW,CAACtE,GAAG,CAAC,CAACoP,SAAc,EAAEC,QAAgB,kBAC1Dzc,OAAA,CAACX,GAAG;kBAEHqd,SAAS,EAAC,KAAK;kBACfC,GAAG,EAAEH,SAAS,CAACI,GAAI;kBACnBC,GAAG,EAAEL,SAAS,CAACM,OAAO,IAAI,OAAQ;kBAClC1C,EAAE,EAAE;oBACHuB,SAAS,EAAEY,SAAS,CAACQ,cAAc,IAAIP,SAAS,CAACO,cAAc,IAAI,OAAO;oBAC1E9N,SAAS,EAAEsN,SAAS,CAACrN,SAAS,IAAI,QAAQ;oBAC1C8N,SAAS,EAAER,SAAS,CAACS,GAAG,IAAI,SAAS;oBACrC;oBACA/W,MAAM,EAAE,GAAGsW,SAAS,CAAC7K,aAAa,IAAI,GAAG,IAAI;oBAC7C6J,UAAU,EAAEgB,SAAS,CAACpL,eAAe,IAAI,SAAS;oBAClD4J,MAAM,EAAE;kBACT,CAAE;kBACFO,OAAO,EAAEA,CAAA,KAAM;oBACd,IAAIgB,SAAS,CAACW,SAAS,EAAE;sBACxB,MAAMnL,SAAS,GAAGwK,SAAS,CAACW,SAAS;sBACrCvV,MAAM,CAACyK,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;oBACxD;kBACD,CAAE;kBACFnG,KAAK,EAAE;oBAAEuN,MAAM,EAAEoD,SAAS,CAACW,SAAS,GAAG,SAAS,GAAG;kBAAU;gBAAE,GAnB1D,GAAGX,SAAS,CAAC9O,EAAE,IAAIgP,QAAQ,EAAE;kBAAA3O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBlC,CACD,CACF,CAAC,EAEAhN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEmM,GAAG,CACxB,CAAC+P,SAAc,EAAErC,KAAU;kBAAA,IAAAsC,qBAAA,EAAAC,sBAAA;kBAAA,OAC1BF,SAAS,CAAC/V,IAAI,iBACbpH,OAAA,CAACL,UAAU;oBACV2d,SAAS,EAAC,eAAe;oBACG;oBAC5BlD,EAAE,EAAE;sBACHnL,SAAS,EAAE,EAAAmO,qBAAA,GAAAD,SAAS,CAACxO,cAAc,cAAAyO,qBAAA,uBAAxBA,qBAAA,CAA0BG,UAAU,KAAI9O,SAAS,CAACQ,SAAS;sBACtEF,KAAK,EAAE,EAAAsO,sBAAA,GAAAF,SAAS,CAACxO,cAAc,cAAA0O,sBAAA,uBAAxBA,sBAAA,CAA0BrO,SAAS,KAAIP,SAAS,CAACM,KAAK;sBAC7DyO,UAAU,EAAE,UAAU;sBACtBC,SAAS,EAAE,YAAY;sBACvBpM,OAAO,EAAE,OAAO;sBAChBqM,QAAQ,EAAE,YAAY;sBACtBC,YAAY,EAAE,YAAY;sBAC1BC,OAAO,EAAE;oBACV,CAAE;oBACFC,uBAAuB,EAAE1O,iBAAiB,CAACgO,SAAS,CAAC/V,IAAI,CAAE,CAAC;kBAAA,GAXvD+V,SAAS,CAAC1P,EAAE,IAAIqN,KAAK;oBAAAhN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAY1B,CACD;gBAAA,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAEL6P,MAAM,CAACC,IAAI,CAACtN,cAAc,CAAC,CAACrD,GAAG,CAAEsD,WAAW;gBAAA,IAAAsN,qBAAA,EAAAC,sBAAA;gBAAA,oBAC5Cje,OAAA,CAACX,GAAG;kBACHqc,GAAG,EAAExX,kBAAmB;kBAExBkW,EAAE,EAAE;oBACHjO,OAAO,EAAE,MAAM;oBACfqK,cAAc,EAAErB,YAAY,EAAA6I,qBAAA,GAACvN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAsN,qBAAA,uBAA9BA,qBAAA,CAAgC9O,SAAS,CAAC;oBACvEmN,QAAQ,EAAE,MAAM;oBAChBrB,MAAM,EAAE,OAAO;oBACfnP,eAAe,GAAAoS,sBAAA,GAAExN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAuN,sBAAA,uBAA9BA,sBAAA,CAAgC7M,eAAe;oBAChEC,OAAO,EAAE,OAAO;oBAChBrL,KAAK,EAAE;kBACR,CAAE;kBAAA0U,QAAA,EAEDjK,cAAc,CAACC,WAAW,CAAC,CAACtD,GAAG,CAAC,CAACG,MAAW,EAAEuN,KAAa;oBAAA,IAAAoD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;oBAAA,oBAC3Dxe,OAAA,CAACV,MAAM;sBAENic,OAAO,EAAEA,CAAA,KAAM3J,kBAAkB,CAACrE,MAAM,CAACkR,YAAY,CAAE;sBACvDvE,OAAO,EAAC,WAAW;sBACnBE,EAAE,EAAE;wBACHY,MAAM,EAAE,eAAe;wBACvBnP,eAAe,EAAE,EAAAqS,qBAAA,GAAA3Q,MAAM,CAACmR,gBAAgB,cAAAR,qBAAA,uBAAvBA,qBAAA,CAAyBS,qBAAqB,KAAI,SAAS;wBAC5E5P,KAAK,EAAE,EAAAoP,sBAAA,GAAA5Q,MAAM,CAACmR,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBS,eAAe,KAAI,MAAM;wBACzDpN,MAAM,EAAE,EAAA4M,sBAAA,GAAA7Q,MAAM,CAACmR,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBS,iBAAiB,KAAI,aAAa;wBACnEvI,QAAQ,EAAE,EAAA+H,sBAAA,GAAA9Q,MAAM,CAACmR,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyBS,QAAQ,KAAI,MAAM;wBACrD9Y,KAAK,EAAE,EAAAsY,sBAAA,GAAA/Q,MAAM,CAACmR,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBxO,KAAK,KAAI,MAAM;wBAC/CuB,OAAO,EAAE,SAAS;wBAClB0N,UAAU,EAAE,QAAQ;wBACpBC,aAAa,EAAE,MAAM;wBACrBnO,YAAY,EAAE,EAAA0N,sBAAA,GAAAhR,MAAM,CAACmR,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBU,YAAY,KAAI,KAAK;wBAC5D1N,SAAS,EAAE,iBAAiB;wBAAE;wBAC9B,SAAS,EAAE;0BACV1F,eAAe,EAAE,EAAA2S,sBAAA,GAAAjR,MAAM,CAACmR,gBAAgB,cAAAF,sBAAA,uBAAvBA,sBAAA,CAAyBG,qBAAqB,KAAI,SAAS;0BAAE;0BAC9EO,OAAO,EAAE,GAAG;0BAAE;0BACd3N,SAAS,EAAE,iBAAiB,CAAE;wBAC/B;sBACD,CAAE;sBAAAmJ,QAAA,EAEDnN,MAAM,CAAC4R;oBAAU,GAtBbrE,KAAK;sBAAAhN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuBH,CAAC;kBAAA,CACT;gBAAC,GArCGyC,WAAW;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCZ,CAAC;cAAA,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC,GAhIL,aAAa1I,cAAc,EAAE;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiIZ,CAAC,EAElBuL,cAAc,IAAI1Y,UAAU,GAAC,CAAC,IAAI+B,gBAAgB,KAAK,MAAM,iBAAI7C,OAAA,CAACX,GAAG;UAAAqb,QAAA,EAAET,cAAc,CAAC;QAAC;UAAAnM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAE,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEG,CACL,eAEDjO,OAAA;MAAA0a,QAAA,EACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA5M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eAEP,CAAC;AAEL,CAAC;AAACjM,EAAA,CA3hDI7B,cAAoC;EAAA,QA8CrCN,cAAc;AAAA;AAAAuf,EAAA,GA9Cbjf,cAAoC;AA6hD1C,eAAeA,cAAc;AAAC,IAAAif,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}